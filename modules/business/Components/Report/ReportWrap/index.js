import I18N from '~I18N';
import { useEffect, useState, useRef } from 'react';
import { Affix, Menu } from 'tntd';
import { getUrlKey } from '@tntd/utils';
const { SubMenu, Item: MenuItem } = Menu;

import './index.less';

export default (props) => {
    const childToken = getUrlKey('childToken');
    const { data, dataList = [], defaultToken, multiMode, onSubjectChange, style = {} } = props;
    const [mode, setMode] = useState('simple'); // 模式
    const [multiAgentList, setMultiAgentList] = useState([]); // 多主体

    const scrollViewArea = useRef();
    const [showRocket, setShowRocket] = useState(false);

    // 根据所有的返回数据，聚合主体关系
    useEffect(() => {
        if (dataList?.length) {
            let multiAgent = [];
            dataList.map((item) => {
                const { baseResultInfo = {}, policyVersionDTO = {} } = item || {};
                const { multi, round, baseFieldInfo = [], tokenId } = baseResultInfo || {};
                // 需要根据custName做子策略聚合
                const customObj = baseFieldInfo?.find((v) => v.name === 'S_S_CUSTNAME') || {};
                const { policyCode, policyName } = policyVersionDTO || {};
                let multiInfo = {
                    ...customObj,
                    title: customObj?.value || '', //customObj?.displayName,
                    multi
                };
                let otherInfo = {
                    tokenId,
                    policyCode,
                    policyName,
                    title: policyName
                };
                // 非主体
                if (!multi) {
                    // 循环模式需要展示在右侧menu进行多主体报告切换
                    if (round) {
                        const hasExistMulti = multiAgent?.find((curMulti) => !curMulti.multi && curMulti.value === multiInfo?.value);
                        if (hasExistMulti && hasExistMulti?.children && hasExistMulti?.children?.length) {
                            hasExistMulti.children.push(otherInfo);
                        } else {
                            multiInfo.children = [otherInfo];
                            multiAgent.push(multiInfo);
                        }
                    }
                } else {
                    multiInfo = {
                        ...otherInfo,
                        ...multiInfo
                    };
                    multiAgent.push(multiInfo);
                }
            });
            setMultiAgentList(multiAgent);
        }
    }, [dataList]);

    // 右侧主体信息展示逻辑
    const renderMenu = (menu) => {
        let iconDom = null;
        if (menu?.multi) {
            iconDom = <i className="report-iconfont icon-body" style={{ 'margin-right': '4px', display: 'inline-block' }} />;
        } else {
            iconDom = <i className="report-iconfont icon-relation" style={{ 'margin-right': '4px', display: 'inline-block' }} />;
        }
        return menu?.children?.length ? (
            <SubMenu
                key={menu.tokenId}
                title={
                    <>
                        {iconDom}
                        {menu.title}
                    </>
                }
                popupClassName="alarm-report-menu-group">
                {menu.children.map((item) => {
                    return renderMenu(item);
                })}
            </SubMenu>
        ) : (
            <MenuItem className="alarm-report-menu-item" curitem={menu} key={menu?.tokenId}>
                <span>
                    {!!menu?.multi && iconDom}
                    {menu?.title || ''}
                </span>
            </MenuItem>
        );
    };

    return (
        <div
            className="report-content-wrap"
            ref={scrollViewArea}
            style={style || {}}
            onScroll={(e) => {
                if (e.target.scrollTop > 0) {
                    setShowRocket(true);
                } else {
                    setShowRocket(false);
                }
            }}>
            {props.children}
            <Affix style={{ position: 'fixed', bottom: 40, right: 8, zIndex: 99 }}>
                <div className="affix-nav">
                    {/* 多主体 */}
                    {multiAgentList?.length > 1 && !childToken && (
                        <div className="affix-nav-item">
                            <Menu
                                selectedKeys={[data?.baseResultInfo?.tokenId]}
                                onClick={({ key }) => {
                                    const reportData = dataList?.find((item) => item?.baseResultInfo?.tokenId === key);
                                    onSubjectChange && onSubjectChange(reportData);
                                }}>
                                <SubMenu
                                    className="multi-agent-root"
                                    popupClassName="report-menu-group"
                                    title={
                                        <span
                                            className={`report-iconfont ${
                                                data?.baseResultInfo?.tokenId === defaultToken ? 'icon-body' : 'icon-relation'
                                            }`}
                                        />
                                    }>
                                    {multiAgentList?.map((mul) => {
                                        return renderMenu(mul);
                                    })}
                                </SubMenu>
                            </Menu>
                        </div>
                    )}

                    {/* 模式 */}
                    {!!multiMode && (
                        <div className="affix-nav-item">
                            <Menu onClick={({ key }) => setMode(key)} selectedKeys={[mode]}>
                                <SubMenu
                                    className="multi-agent-root"
                                    popupClassName="alarm-report-menu-group"
                                    title={<span className={`report-iconfont ${mode === 'simple' ? 'icon-simple' : 'icon-detailed'}`} />}>
                                    <MenuItem className="alarm-report-menu-item" key="simple">
                                        <span className="report-iconfont icon-simple mode-menu-item-icon" />{' '}
                                        {I18N.reportwrap.index.jianYiMoShi}
                                    </MenuItem>
                                    <MenuItem className="alarm-report-menu-item" key="detail">
                                        <span className="report-iconfont icon-detailed mode-menu-item-icon" />{' '}
                                        {I18N.reportwrap.index.xiangXiMoShi}
                                    </MenuItem>
                                </SubMenu>
                            </Menu>
                        </div>
                    )}

                    {/* 小火箭 */}
                    {showRocket && (
                        <div
                            className="affix-nav-item"
                            onClick={() => {
                                scrollViewArea.current.scrollTop = 0;
                            }}>
                            <span className="report-iconfont icon-to-top" />
                        </div>
                    )}
                </div>
            </Affix>
        </div>
    );
};
