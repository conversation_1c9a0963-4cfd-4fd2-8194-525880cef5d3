export default {
  dataconvert: {
    jiXuBuChong: 'Continue complement',
    ceLuePeiZhiBu: 'Strategy configuration cannot be empty',
    ceLuePeiZhiBu2: 'Invalid strategy configuration, reasons are as follows:',
    liuChengMuBan: 'Process Template',
    dongZuo: 'Action',
    xuYaoPeiZhiZhi: '] At least 2 branches need to be configured',
    xuYaoPeiZhiTiao: '] 1 champion branch needs to be configured',
    xuYaoQieZhiNeng:
      '] 1 default branch needs to be configured and can only be 1',
    guanJunTiaoZhanZhe:
      '[Champion Challenger] Condition configuration not completed',
    guanJunTiaoZhanZhe2: 'Champion challenger',
    jueCeGongJuWei: '[Decision Tool] Execution data not set',
    jueCeGongJu: 'Decision tool',
    hanShuWeiSheZhi: '[Function] Execution data not set',
    hanShu: 'Function',
    moXingWeiSheZhi: '[Model] Execution data not set',
    moXing: 'Model',
    sanFangFuWuWei: '[External Service] Execution data not set',
    sanFangFuWu: 'External service',
    guiZeJiWeiShe: '[Rule Set] Execution data not set',
    guiZeJi: 'Rule Set',
    xian: 'Line',
    shuRu: 'Enter',
    shuChu: 'Output',
    zhiShaoPeiZhiTiao: '] At least 2 configurations',
    kaiShi: 'Start',
    jieShu: 'End',
    bingXing: 'Parallel',
    bingXing2: 'Parallel',
    panDuanKaiShiTiao: '[Judgement Start] Condition configuration not filled',
    panDuan: 'Judge',
    jieShuQueShaoShu: '[End] Input flow missing',
    kaiShiKaiShiJie: '[Start] Only one start node allowed',
    kaiShiQueShaoShu: '[Start] Output flow missing',
    queShaoShuChuLiu: '] Output flow missing',
    queShaoShuRuLiu: '] Input flow missing',
    queShaoShuRuShu: '] Input/output flow missing',
    bingXingJieShu: 'End of Parallel',
    bingXingKaiShi: 'Start Parallel',
    panDuanJieShu: 'End of Judgment',
    panDuanKaiShi: 'Start Judgment',
  },
  index: {
    danJiLiuChengMo:
      'Click the process template to display the trajectory diagram at the bottom of the page',
    jieXiShuJuCuo: 'Parsing data error,',
  },
};
