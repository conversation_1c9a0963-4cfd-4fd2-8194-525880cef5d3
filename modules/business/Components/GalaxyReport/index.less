.report-title {
    margin: 8px 0px;
}
.report-detail {
    .report-head-info-wrap{
        background-image: linear-gradient(0deg, #F9FCFF 28%, #EBF4FF 54%, #CCE2FF 100%);
        position:relative;
        .top-bg{
            background-image: url(../Report/ReportHeader/images/blue-top-bg.svg);
        }
        .report-head-info{
            background-image: url(../Report/ReportHeader/images/blue-report-head.svg);
            padding:'none';
            background-position: right bottom;
            background-repeat: no-repeat;
            .btn-right {
                border-radius: 0px 0px 0px var(---LG, 16px);
                background: linear-gradient(180deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.30) 100%);
                box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.05);
                position: absolute;
                right: 0;
                top: 0;
            }
            .report-header-detail {
                display:flex;
                flex-direction: row;
                align-items: center;
                width:303px;
                color:#8B919E;
                text-shadow: 0px 0px 20px rgba(0, 0, 0, 0.05);
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
                .inner {
                    margin-right: 24px;
                }
            }
        }
    }
    .report-item-box{
        border-radius: 8px;
        background: white;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.05);
        margin-top: 16px;
        padding:16px;
        align-items: flex-start;
        .head-example{
            height: 16px;
            width: 16px;
        }
        .analyze{
            display: flex;
            align-items: center;
            height: 100%;
            .analyze-content{
                display: flex;
                height: 22px;
                padding: 1px 8px;
                justify-content: center;
                align-items: center;
                display: flex;
                height: 22px;
                padding: 1px 8px;
                justify-content: center;
                align-items: center;
            }
        }
        .description {
            background-color: #F8F9FB;
        }
        .report-table {
            display:flex;
            flex-direction: row;
            align-items: center;
        }
    }
}
