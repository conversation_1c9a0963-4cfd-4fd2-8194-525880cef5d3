.tntd-description-divider {
    &.description-item-wrap {
        padding: 12px;
        background: #fff;
        margin: 12px 0 0 0;
        border-radius: @border-radius;
        .ant-descriptions-title {
            margin-top: 0;
        }
    }
    .ant-descriptions-title {
        margin-top: 20px;
        margin-bottom: 4px;
    }
    .ant-descriptions-item {
        .tnt-descriptions-label-title{
            display:none;
        }
        .ant-descriptions-item-content {
            display: flex;
            width: 100%;
            >.tntd-ellipsis, .tnt-ellipsis{
                width: 100%;
            }

            .label-title {
                max-width: 70%;
                margin-right: 4px;
                color: #8B919E;

                >.tntd-ellipsis, >.tnt-ellipsis {
                    align-items: center;
                    .anticon {
                        margin-left: 2px;
                    }
                }
                &+.ellipsis-nowrap:not(.is-not-flex){
                    flex:1;
                }
            }
        }
    }
}
