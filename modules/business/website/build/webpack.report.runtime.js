const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const HtmlInlineScriptPlugin = require('html-inline-script-webpack-plugin');
// 开发模式
const isDev = process.env.NODE_ENV === 'development';

let webpackConfig = {
    entry: path.resolve(__dirname, '../../Components/Report/ReportRuntime.js'),
    output: {
        path: path.resolve(__dirname, '../../Components/Report/report_library'),
        clean: true,
        publicPath: ''
    },
    // mode: 'production',
    context: path.resolve(__dirname, '../'),
    devServer: {
        port: 3030,
        open: true,
        static: [path.join(__dirname, '../public')]
    },
    mode: isDev ? 'development' : 'production',
    devtool: isDev ? 'source-map' : false,
    module: {
        rules: [
            {
                test: /\.(png|svg|jpg|gif)$/,
                type: 'asset/inline',
                parser: {
                    dataUrlCondition: {
                        maxSize: 2000000
                    }
                }
            },
            {
                test: /\.js$/,
                exclude: /(node_modules|bower_components)/,
                use: ['babel-loader']
            },
            {
                test: /(\.less|\.css)$/,
                use: [
                    { loader: 'style-loader' },
                    {
                        loader: 'css-loader',
                        options: {
                            modules: 'global'
                        }
                    },
                    {
                        loader: 'less-loader',
                        options: {
                            lessOptions: {
                                javascriptEnabled: true,
                                modules: true,
                                localIndexName: '[name]__[local]___[chunkhash:base64:5]',
                                modifyVars: {
                                    hack: 'true; @import "~tntd/themes/default/variables.less";'
                                }
                            }
                        }
                    }
                ]
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset/inline', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                }
            }
        ]
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, '../../website/src'),
            '~modules': path.resolve(__dirname, '../../Components'),
            '~I18N': path.resolve(__dirname, '../../I18N.js'),
            tntd: 'tntd-v3'
        }
    },
    plugins: [
        new webpack.ContextReplacementPlugin(/moment[\/\\]locale$/, /zh-cn/),
        new HtmlWebpackPlugin({
            filename: 'report.jst',
            inject: 'body',
            template: path.resolve(__dirname, '../../Components/Report/report.html')
        }),
        new HtmlInlineScriptPlugin({
            htmlMatchPattern: [/report.jst$/]
        }),
        new webpack.ProvidePlugin({
            React: 'react'
        })
    ]
};

module.exports = webpackConfig;
