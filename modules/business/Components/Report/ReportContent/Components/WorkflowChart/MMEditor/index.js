import I18N from '~I18N';
import { PureComponent, createRef } from 'react';
import './index.less';
// import MMEditor from 'mmeditor';
import MMEditor from './src/MMEditor';
import { message, Tooltip, Handle } from 'tntd';
import { isEqual } from 'lodash';
import initShapes from './MMShapes/initShapes';
import DataConvert from './DataConvert';
import { nodeTypeMap, NodeNameMap } from '../../../../constant';

import JumpMaps from './JumpMaps';

class Editor extends PureComponent {
    state = {};
    // 编辑器实例
    editor = {};

    operaHandleRef = createRef();

    // 销毁
    componentWillUnmount() {
        this.editor.destroy();
        this.editor = null;
    }

    componentDidUpdate(props) {
        if (
            !isEqual(props.graphData, this.props.graphData) ||
            !isEqual(props.mainWorkflow, this.props.mainWorkflow) ||
            !isEqual(props.tokenId, this.props.tokenId)
        ) {
            this.props.graphData && this.setData(this.props.graphData);
        }
    }

    // 初始化editor
    async componentDidMount() {
        this.editor = new MMEditor({
            dom: this.editorRef,
            showMiniMap: true,
            mode: this.props.type === 'chart' ? 'view' : 'edit' // 只读模式设置 mode:"view"
        });
        initShapes(this.editor);
        this.setData(this.props?.graphData);
        this.addEditorEvent();
        this.setState({
            init: true
        });
        this.props.onRef && this.props.onRef(this);
    }

    jumpPage = () => {
        JumpMaps[this.state.nowTextNode.type].go(this.state.nowTextNode.data);
    };

    setData = async (data) => {
        try {
            await this.editor.schema.setInitData(
                DataConvert.convert(typeof data === 'object' ? data : JSON.parse(data || '{}'), this.editor)
            );
            if (!this.props.componentLog) {
                await this.editor.controller.autoFit();
            }
        } catch (e) {
            message.error(I18N.mmeditor.index.jieXiShuJuCuo + e.message);
        }
    };
    showOperaEvent = () => {
        this.setState({
            nodeOpera: true
        });
    };
    hideOperaEvent = () => {
        this.setState({
            nodeOpera: false,
            jumpEyeVisible: false
        });
    };

    // 初始化编辑器事件
    addEditorEvent() {
        // 选中
        this.editor.graph.on('node:click', ({ node }) => {
            this.props.onNodeClick && this.props.onNodeClick(node.data);
        });

        this.editor.graph.on('node:mouseenter', ({ node }) => {
            const bbox = node?.node?.getBoundingClientRect?.();

            const canJump = this.props.canJump && !window.downMode && JumpMaps[node.data.type];

            const defaultNodeName = NodeNameMap[node.data.type];
            const newNodeData = { ...(node.data || {}), defaultNodeName };

            if (node.data.type !== 'SubDecisionFlowNode') {
                return this.setState(
                    {
                        jumpEyeVisible: canJump,
                        nowTextNode: newNodeData,
                        textVisible: true,
                        textX: bbox.x + bbox.width / 2,
                        textY: bbox.y - (canJump ? 30 / 2 : -5)
                    },
                    () => {
                        this.setState({
                            nodeOpera: true,
                            x: bbox.x + bbox.width / 2 - this.operaHandleRef?.current?.clientWidth / 2,
                            y: bbox.y - this.operaHandleRef?.current?.clientHeight + 6
                        });
                    }
                );
            }

            this.setState({
                nowTextNode: newNodeData,
                textVisible: true,
                textX: bbox.x + bbox.width / 2,
                textY: bbox.y, // 40,

                x: bbox.x + 70,
                y: bbox.y - 45
            });
        });

        this.editor.graph.on('node:mouseleave', () => {
            this.setState({
                textVisible: false,
                nodeOpera: false,
                lineInfo: null
            });
        });

        // 线条移入事件
        // line鼠标移入事件
        this.editor.graph.on('line:mouseenter', ({ line }) => {
            const { label } = line.data;
            if (label) {
                const bbox = line.node.querySelector('.mm-line-label')?.getBoundingClientRect?.();
                if (bbox) {
                    this.setState({
                        lineInfo: {
                            x: bbox.x + bbox.width / 2,
                            y: bbox.y - this.operaHandleRef?.current?.clientHeight + 12,
                            title: label
                        }
                    });
                }
            }
        });

        // line鼠标移出事件
        this.editor.graph.on('line:mouseleave', () => {
            this.setState({ lineInfo: {} });
        });

        this.operaHandleRef.current.addEventListener('mouseenter', this.showOperaEvent);
        this.operaHandleRef.current.addEventListener('mouseleave', this.hideOperaEvent);

        this.setState({
            init: true
        });
    }

    render() {
        const { nowTextNode, lineInfo, nodeOpera, x, y, jumpEyeVisible } = this.state;
        return (
            <div
                className="job-editor report-job-editor"
                ref={(ref) => {
                    this.ref = ref;
                }}>
                <svg width="0" height="0">
                    <defs>
                        <filter id="mm-editor-node-shadow" x="-50%" y="-50%" width="200%" height="200%">
                            <feGaussianBlur in="SourceAlpha" stdDeviation="10" />
                            <feOffset dx="0" dy="0" result="offsetblur" />
                            <feFlood floodColor="rgba(0, 0, 0, 0.05)" />
                            <feComposite in2="offsetblur" operator="in" />
                            <feMerge>
                                <feMergeNode />
                                <feMergeNode in="SourceGraphic" />
                            </feMerge>
                        </filter>
                    </defs>
                </svg>
                <div className="job-content flow-editor-content">
                    <div
                        className="job-mm-editor"
                        ref={(ref) => {
                            this.editorRef = ref;
                        }}
                    />
                </div>

                <div
                    ref={this.operaHandleRef}
                    style={{
                        visibility: nodeOpera ? 'visible' : 'hidden',
                        position: 'fixed',
                        height: '28px',
                        left: x,
                        top: y,
                        border: 'none'
                    }}>
                    <Handle className="editor-node-opera-wrap" lang="cn">
                        {jumpEyeVisible && <span className="editor-node-opera node-opera-jump" onClick={this.jumpPage} />}
                    </Handle>
                </div>

                {/* 节点文案tooltip */}
                <div
                    style={{
                        position: 'fixed',
                        left: this.state.textX,
                        top: this.state.textY,
                        display: this.state.textVisible ? 'block' : 'none'
                    }}>
                    <Tooltip
                        visible={this.state.textVisible}
                        title={
                            <>
                                {nowTextNode?.defaultNodeName && nowTextNode?.defaultNodeName !== nowTextNode?.name ? (
                                    <span className="node-name-type">{nowTextNode?.defaultNodeName}：</span>
                                ) : (
                                    ''
                                )}
                                {nowTextNode?.name}
                            </>
                        }
                    />
                </div>

                {/* 线条 信息 */}
                <Tooltip title={lineInfo && lineInfo?.title} visible={true} overlayClassName="line-label-max-300">
                    <div
                        style={{
                            position: 'fixed',
                            top: lineInfo ? lineInfo?.y : 0,
                            left: lineInfo ? lineInfo?.x : 0,
                            display: lineInfo && lineInfo?.title ? 'block' : 'none'
                        }}
                    />
                </Tooltip>
            </div>
        );
    }
}
export default Editor;
