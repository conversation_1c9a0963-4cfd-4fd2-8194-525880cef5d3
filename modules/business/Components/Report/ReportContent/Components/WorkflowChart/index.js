/*
 * @CreatDate: 2019-03-01 11:30:17
 * @Describe: 策略流程图模块
 */
import I18N from '~I18N';
import { cloneDeep } from 'lodash';
import { useEffect, useState, useRef } from 'react';
import { Spin, Button, Popover, Row, Select } from 'tntd';
import TooltipSelect from '@tntd/tooltip-select';
import NodeViewDrawer from '../NodeViewDrawer';
import MMEditor from './MMEditor';

import './index.less';

const { Option } = Select;
function WorkflowChart(props) {
    const { flowLogData, flowLogLoading, getFlowLog, tokenId, graphJson, opera = [], canJump, setNewFlowLogData } = props;
    const [node, setNode] = useState();
    const [visible, setVisible] = useState();
    const editor = useRef();
    const changeColor = useRef();
    const popDomRef = useRef();
    const { fieldMap = {} } = flowLogData || {};

    useEffect(() => {
        if (tokenId && editor?.current) {
            changeColor.current = queryWorkFlowData.bind(this, props);
            editor?.current?.on('change', changeColor?.current);
            return () => {
                editor?.current?.off && changeColor?.current && editor?.current?.off('change', changeColor.current);
            };
        }
    }, [tokenId]);

    async function queryWorkFlowData(p) {
        editor?.current?.off('change', queryWorkFlowData);
        editor?.current?.off('change', changeColor.current);
        let flowLogDataTemp = flowLogData;
        if (getFlowLog) {
            let curFlowLogDataTemp = await getFlowLog(p?.tokenId);
            if (curFlowLogDataTemp && Array.isArray(curFlowLogDataTemp)) {
                if (curFlowLogDataTemp[0]) {
                    flowLogDataTemp = curFlowLogDataTemp[0];
                }
            } else {
                flowLogDataTemp = curFlowLogDataTemp;
            }
        }
        initStyle(flowLogDataTemp);
        editor?.current?.controller?.autoFit();
    }

    const initStyle = (data) => {
        if (editor?.current?.graph) {
            const { diagramLine = [], flowModelinAndOutputParams = [] } = data || {};
            flowModelinAndOutputParams?.forEach((item) => {
                const node = editor?.current?.graph?.node?.nodes?.[item.nodeId];
                if (node?.node) {
                    node && node.node?.classList.add(item.taskSuccess ? 'green' : 'red');
                    node.data.className = item.taskSuccess ? 'green' : 'red';
                }
            });
            for (let key in editor?.current?.graph.line.lines) {
                const line = editor?.current?.graph?.line?.lines?.[key];
                const findLineId = diagramLine.indexOf(key);
                if (findLineId > -1) {
                    line.data.className = 'green';
                    editor?.current?.graph.line.updateLine(key);
                }
            }
        }
    };

    // 节点点击
    const onNodeClick = (data) => {
        const item = flowLogData?.flowModelinAndOutputParams?.find((item) => item.nodeId === data.uuid);
        // 点击 设置查看节点信息
        if (item) {
            setVisible(true);
            setNode({ ...data });
        }
        if (data.type === 'SubDecisionFlowNode' && props.setSubflowData) {
            if (flowLogData) {
                let newData = cloneDeep(flowLogData);
                //改造新的flowLogData
                let newNode = flowLogData?.flowModelinAndOutputParams?.find((v) => v.nodeId === data.data.id);
                newData.flowModelinAndOutputParams = newNode?.result;
                let newMap = {};
                newData.flowModelinAndOutputParams?.map((v) => {
                    v.nodeInputList.map((item) => {
                        if (newMap[item?.name]?.nodeIdList) {
                            newMap[item?.name]?.nodeIdList.push(v.nodeId);
                        } else {
                            newMap[item?.name] = {
                                ...item,
                                nodeIdList: [v.nodeId]
                            };
                        }
                    });
                    v.nodeOutputList.map((item) => {
                        if (newMap[item?.name]?.nodeIdList) {
                            newMap[item?.name]?.nodeIdList.push(v.nodeId);
                        } else {
                            newMap[item?.name] = {
                                ...item,
                                nodeIdList: [v.nodeId]
                            };
                        }
                    });
                });
                newData.fieldMap = newMap;
                newData.diagramLine = newNode?.extension?.diagramLine;
                setNewFlowLogData(newData);
            }
            props.setSubflowData({
                graphJson: data.data,
                visible: true
            });
        }
    };

    // 根据搜索定位到节点
    const toPosition = (positionNodes) => {
        let positionFlag = true;
        Object.values(editor?.current?.graph.node.nodes).forEach((v) => {
            if (v.node.classList?.contains('show-highlight')) {
                v.node.classList.remove('show-highlight');
                if (v.node?.data) {
                    v.node.data.className = node.data.className.replace('show-highlight', '');
                }
            }
        });
        if (positionNodes?.length) {
            positionNodes?.forEach((nodeId) => {
                const node = editor?.current?.graph?.node?.nodes?.[nodeId];
                node && node.node.classList.add('show-highlight');
                if (node?.data) {
                    if (positionFlag) {
                        let domBox = editor?.current?.dom.node.getBoundingClientRect();
                        editor?.current?.controller.moveTo(-node.data.x + domBox.width / 2 - 50, -node.data.y + domBox.height / 2 - 30);
                        positionFlag = false;
                    }
                    node.data.className = node.data.className + ' show-highlight';
                }
            });
        }
    };

    return (
        <div className="report-detail-flow">
            <Spin spinning={flowLogLoading}>
                <MMEditor
                    canJump={canJump}
                    type={'chart'}
                    dependence={false}
                    onRef={(ref) => {
                        editor.current = ref.editor;
                    }}
                    ifHideDialog={true}
                    onNodeClick={onNodeClick}
                    componentLog={true}
                    graphData={graphJson}
                    tokenId={tokenId}
                />

                <NodeViewDrawer {...props} visible={visible} data={flowLogData} activeNode={node} onClose={() => setVisible(false)} />

                <div className="editor-fun-opera" ref={popDomRef}>
                    <Button.Group>
                        <Popover
                            trigger="click"
                            placement="left"
                            getTooltipContainer={() => popDomRef.current}
                            content={
                                <Row type="flex" align="middle">
                                    <TooltipSelect
                                        isVirtual
                                        showSearch
                                        optionFilterProp="children"
                                        placeholder={I18N.workflowchart.index.qingXuanZeZiDuan}
                                        style={{ width: '200px' }}
                                        allowClear={true}
                                        onChange={(v, _this) => {
                                            toPosition(_this?.props?.nodes || []);
                                        }}>
                                        {Object.values(fieldMap || {})?.map((v) => {
                                            return (
                                                <Option value={v.name} key={v.name} nodes={v?.nodeIdList || []}>
                                                    {v?.displayName}
                                                </Option>
                                            );
                                        })}
                                    </TooltipSelect>
                                </Row>
                            }>
                            <Button>
                                <span className="tiangong-iconfont icon-locate1" />
                                {I18N.workflowchart.index.ziDuanDingWei}
                            </Button>
                        </Popover>
                        {opera || []}
                    </Button.Group>
                </div>
            </Spin>
        </div>
    );
}

export default WorkflowChart;
