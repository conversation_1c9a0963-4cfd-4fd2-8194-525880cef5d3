import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Drawer, Row, Col, TntdForm, TntdCascader, message, Checkbox, InputNumber, Badge, Icon, Tabs, Tag, Title } from 'tntd';
import './index.less';
export default function ProductModal({ visible, record, onCancel, onSave }) {
    const { TabPane } = Tabs;
    const [form] = TntdForm.useForm();
    const [type, setType] = useState('up');
    const [value, setValue] = useState(1);
    const [customVisible, setCustomVisible] = useState(false);
    const dataSource = [];
    const onOk = () => {
        form.validateFields().then((values) => {
            onSave(values);
        });
    };

    const columns = [
        {
            title: '客户名称',
            dataIndex: 'custName',
            ellipsis: true,
            width: 200
        },
        {
            title: '客户编号',
            dataIndex: 'custNo',
            width: 160,
            ellipsis: true
        },
        {
            title: '证件号码',
            dataIndex: 'custNo',
            width: 160,
            ellipsis: true
        },
        {
            title: '客户经理',
            dataIndex: 'custNo',
            width: 160,
            ellipsis: true
        },
        {
            title: '操作',
            dataIndex: 'custNo',
            width: 160,
            ellipsis: true
        }
    ];
    // const autoGeneration = TntdForm.useWatch('autoGeneration', form);

    return (
        <>
            <Drawer title={'详情'} visible={visible} destroyOnClose onClose={onCancel} onOk={onOk} width={640} className="drawer-container">
                <div className="drawer-item">
                    <Title title="信号详情" />
                    <div className="signal-details">
                        <Row>
                            <Col span={12} className="singnal-inner">
                                <div className="icon-margin" style={{ backgroundColor: '#E6F4FF' }}>
                                    <Icon type="warning" style={{ fontSize: '24px', color: '#126BFB' }} />
                                </div>
                                <div className="msg">
                                    <div className="signal-label">预警等级</div>
                                    <div className="signal-value">{'流动比率下降'}</div>
                                </div>
                            </Col>
                            <Col span={12} className="singnal-inner">
                                <div className="icon-margin" style={{ backgroundColor: '#FFF6ED' }}>
                                    <Icon type="alarm" style={{ fontSize: '24px', color: '#F47345' }} />
                                </div>
                                <div className="msg">
                                    <div className="signal-label">预警等级</div>
                                    <div className="signal-value">
                                        <Badge text={'一级'} color={'red'} />
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </div>
                    <div className="alarm-valid">
                        <Title title="预警有效性" />
                        <Tag size="small" color="volcano">
                            {'武警'}
                        </Tag>
                    </div>
                    {1231231321321231231}
                    <div className="signal-annex">
                        <div className="signal-title">{'附件'}</div>
                        <div>{'附件名称'}</div>
                        <div>
                            <Button>
                                <Icon type="download" />
                            </Button>
                        </div>
                    </div>
                </div>
                <div className="drawer-item">
                    <Tabs defaultActiveKey="1">
                        <TabPane tab="tab 1" key="1">
                            Tab 1
                        </TabPane>
                        <TabPane tab="tab 2" key="2">
                            Tab 2
                        </TabPane>
                        <TabPane tab="tab 3" key="3">
                            Tab 3
                        </TabPane>
                    </Tabs>
                </div>
            </Drawer>
        </>
    );
}
