export default {
  constant: {
    jueCeGongJuZhi: 'Decision Tool Execution Information',
    pingFenKaZhiXing: 'Scorecard Execution Information',
    sanFangZhiXingXin: 'External Data Execution Information',
    zhiBiaoXinXi: 'Indicator Information',
    hanShuKuZhiXing: 'Function Library Execution Information',
    guiZeZhiXingXin: 'Rule Execution Information',
    xinXiZongLan: 'Information Overview',
    ziCeLueZhiXing: 'Substrategy Execution Information',
    moXingZhiXingXin: 'Model Execution Information',
    jueCeGongJuMing: 'Decision Tool Name',
    pingFenKaMingCheng: 'Scorecard Name',
    waiShuZhiBiaoMing: 'External Name',
    sanFangMingCheng: 'External Name',
    zhiBiaoMingCheng: 'Indicator Name',
    hanShuKuMingCheng: 'Function Library Name',
    guiZeJiMingCheng: 'Rule Set Name',
    ziCeLueMingCheng: 'Substrategy Name',
    moXingMingCheng: 'Model Name',
    hanShuKu: 'Function',
    sanFang: 'Three-Party Service',
    ziCeLue: 'Substrategy',
    ziLiuCheng: 'Sub-process',
  },
  index: {
    shiJian: 'Time',
    bianHao: 'TokenId',
  },
};
