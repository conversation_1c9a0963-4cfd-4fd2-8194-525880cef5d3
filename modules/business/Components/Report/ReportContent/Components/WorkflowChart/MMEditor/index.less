@hover-back: #e9eefa;
@back-color: #f7f8fa;
@font-2-color: rgba(23, 35, 61, 0.8);
@font-2-color-hover: rgba(23, 35, 61, 0.5);
@font-3-color: #546470;
.job-editor.report-job-editor {
    display: flex;
    height: calc(100vh - 150px);
    user-select: none;
    background: #fff;
    width: 100%;

    .wrap-txt-node {
        font-size: 12px;
        line-height: 15px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        word-break: break-all;
        -webkit-line-clamp: 2;
        font-family: sans-serif;
    }
    svg text {
        font-size: 12px;
        text-anchor: middle;
        dominant-baseline: middle;
    }

    .job-content {
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        .job-mm-editor {
            width: 100%;
            flex-grow: 1;
            .mm-editor .mm-editor-back{
                // background-image:url(./MMShapes/images/point.png) !important;
                background-size: 8px;
            }
            .mm-minimap {
                height: 127px;
                border: 1px solid #e9edf3;
                bottom: 10px;
                top: auto;
                border-radius: 0;
            }
            .mm-node {
                &.red rect{
                    stroke: red;
                }
                &.show-highlight {
                    .mm-highlight {
                        display: block;
                        width: 18px;
                        height: 18px;
                        border-radius: 100%;
                        background-image: url(./MMShapes/images/locate.svg);
                        background-size: cover;
                        background-repeat: no-repeat;
                    }
                }
                .mm-node-shape {
                    span.iconfont {
                        display: none;
                    }
                    &.success {
                        .etl-success {
                            display: block;
                            color: green;
                        }
                        .icon-node {
                            stroke: green;
                            fill: #fff;
                        }
                    }
                    &.error {
                        .etl-baocuo {
                            display: block;
                            color: red;
                        }
                        .icon-node {
                            stroke: red;
                        }
                    }
                    &.running {
                        .icon-node {
                            stroke-dasharray: 100;
                            stroke-linecap: round;
                            stroke-linejoin: round;
                            stroke-width: 2px;
                            animation: dashing 2s ease-in-out infinite;
                        }
                    }
                }
            }
            .mm-line {
                &.success path {
                    stroke: green !important;
                }
                &.error path {
                    stroke: red !important;
                }
                &.running {
                    path {
                        stroke: #4c79ff !important;
                    }
                    .mm-line-arrow {
                        animation: shink 1s ease-in-out infinite alternate;
                    }
                }
            }

            .exclusive-node-icon{
                path{
                    fill:#F47345
                }
            }
            .parallel-node-icon{
                path{
                    fill:#7A5AF8
                }
            }

        }
    }

    .editor-node-opera-wrap {
        white-space: nowrap;
        display: flex;
        align-items: center;
        background-color: #fff;
        padding: 2px 4px;
        .editor-node-opera {
            width: 16px;
            height: 16px;
            background-repeat: no-repeat;
            background-size: cover;
            display: inline-block;
            cursor: pointer;
            &.node-opera-jump {
                background-image: url(./MMShapes/images/jump.svg);
            }
        }
    }
}


.line-label-max-300 {
    max-width: 300px;
}

@keyframes dashing {
    from {
        stroke-dashoffset: 0;
    }
    to {
        stroke-dashoffset: 200;
    }
}
@keyframes runCircle {
    from {
        stroke-dasharray: 1, 395;
    }
    to {
        stroke-dasharray: 395, 395;
    }
}
@keyframes shink {
    from {
        opacity: 0.5;
    }
    to {
        opacity: 1;
    }
}

.node-name-type{
    color:@blue-3;
}
