const webpack = require('webpack');
const merge = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

const CopyPlugin = require('copy-webpack-plugin');
const BranchPlugin = require('@tntd/webpack-branch-plugin');
const baseWebpackConfig = require('./webpack.base.conf');
const config = require('./config');

const projectCode = 'modules'; // 对应应用的标识

module.exports = merge(baseWebpackConfig, {
    mode: 'production',
    output: {
        chunkFilename: config.common.sourcePrefix + '[name].[chunkhash:8].js',
        publicPath: config.build.assetsPublicPath,
        library: projectCode,
        libraryTarget: 'umd',
        clean: true,
        chunkLoadingGlobal: `webpackJsonp_${projectCode}`
    },
    optimization: {
        splitChunks: {
            cacheGroups: {
                styles: {
                    name: 'styles',
                    test: /\.(css|less)(\?.*)?$/,
                    chunks: 'all',
                    enforce: true
                }
            }
        },
        minimize: true,
        minimizer: [
            new TerserPlugin({
                extractComments: false
            }),
            new CssMinimizerPlugin()
        ]
    },
    plugins: [
        new webpack.ContextReplacementPlugin(/moment[\/\\]locale$/, /zh-en|en-us/),
        new HtmlWebpackPlugin({
            filename: config.common.sourcePrefix + 'index.html',
            template: config.common.htmlTemplatePath, // 配置html模板的地址
            scriptLoading: 'blocking',
            chunksSortMode: 'none'
        }),
        new MiniCssExtractPlugin({
            filename: config.common.sourcePrefix + '[name].[chunkhash:8].css',
            ignoreOrder: true
        }),
        new CopyPlugin({
            patterns: [{ from: 'public', to: '' }]
        }),
        new BranchPlugin({
            filename: config.common.sourcePrefix + 'branch_info.txt'
        })
    ]
});
