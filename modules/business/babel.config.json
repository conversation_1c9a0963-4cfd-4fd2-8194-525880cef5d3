{
    "presets": [
        [
            "@babel/preset-env",
            {
                "corejs": {
                    "version": 3
                },
                "useBuiltIns": "usage",
                "targets": {
                    "ie": 9,
                    "chrome": 52,
                    "safari": 10,
                    "opera": 32,
                    "firefox": 30
                }
            }
        ],
        "@babel/preset-react"
	],
	"env": {
        "development": {
            "plugins": []
        },
        "production": {
            "plugins": [
                [
                    "transform-remove-console",
                    {
                        "exclude": [
                            "error",
                            "warn",
                            "info"
                        ]
                    }
                ]
            ]
        }
    },
    "plugins": [
        [
            "import",
            {
				"libraryName": "antd",
				"libraryDirectory": "es"
			},
            "antd"
        ],
        [
            "import",
            {
                "libraryName": "tntd",
                "libraryDirectory": "es"
            },
            "tntd"
        ],
        "@babel/plugin-syntax-dynamic-import",
        [
            "@babel/plugin-proposal-class-properties",
            {
                "loose": true
            }
        ],
		[
			"@babel/plugin-proposal-optional-chaining",
			{
				"loose": true
			}
		],
        ["@babel/plugin-proposal-private-property-in-object", { "loose": true }],
        ["@babel/plugin-proposal-private-methods", { "loose": true }],
    ]
}
