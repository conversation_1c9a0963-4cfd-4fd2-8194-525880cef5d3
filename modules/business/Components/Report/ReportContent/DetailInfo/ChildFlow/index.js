import I18N from '~I18N';
import { useEffect, useState } from 'react';
import { Table, Tooltip, Spin, Empty, Ellipsis, Tag, Descriptions, Icon } from 'tntd';
import history from '../../../../../website/src/utils/history.js';
import { searchToObject } from '@/utils/utils';
import InfoSection, { InfoTitle } from '../../Components/InfoSection';
import './index.less';

const getParams = (params) => {
    let paramStr = '';
    Object.keys(params).forEach((item) => {
        if (paramStr === '') {
            paramStr = `${item}=${params[item]}`;
        } else {
            paramStr = `${paramStr}&${item}=${params[item]}`;
        }
    });
    return paramStr;
};

export default (props) => {
    const { runDataInfo, runDataInfoLoading, versionToken } = props;
    const [data, setData] = useState();
    const { cycle = [], normal = [] } = data || {};
    const searchObj = searchToObject(location?.search);
    const { token, type, ...rest } = searchObj || {};
    const searchParams = getParams(rest);
    useEffect(() => {
        if (!runDataInfoLoading && runDataInfo) {
            const dataInfo = {
                cycle: [],
                normal: []
            };
            runDataInfo?.forEach((v) => {
                if (v.multi) {
                    dataInfo.cycle.push(v);
                } else {
                    dataInfo.normal.push(v);
                }
            });
            setData(dataInfo);
        }
    }, [runDataInfo, runDataInfoLoading]);

    const columns = [
        {
            title: I18N.childflow.index.ziCeLueMingCheng,
            width: '50%',
            dataIndex: 'policyName',
            ellipsis: true,
            render: (value, record) => {
                value = (value || '') + (record?.policyCode ? `(${record?.policyCode || ''})` : '');
                const { extension, reasonMessage, reasonCode } = record || {};
                const { version } = extension || {};
                return (
                    <Ellipsis
                        className="ellipsis-align-center"
                        widthLimit="100%"
                        prefix={
                            version && (
                                <Tag
                                    color="purple"
                                    style={{
                                        background: 'rgba(148,95,185,0.10)',
                                        border: '1px solid rgba(148,95,185,0.4)'
                                    }}>
                                    V{version}
                                </Tag>
                            )
                        }
                        suffix={
                            reasonCode !== 200 &&
                            !!reasonMessage && (
                                <Tooltip title={reasonMessage}>
                                    <Icon type="info-circle" />
                                </Tooltip>
                            )
                        }
                        title={value}
                    />
                );
            }
        },
        {
            title: I18N.childflow.index.mingZhongJieGuo,
            width: '30%',
            dataIndex: 'finalDecision',
            ellipsis: true,
            render: (value) => {
                return (
                    <Tooltip title={value} placement="topLeft">
                        {value}
                    </Tooltip>
                );
            }
        }
    ];

    if (!window.downMode) {
        columns.push({
            title: I18N.childflow.index.caoZuo,
            width: '20%',
            ellipsis: true,
            render: (record) => {
                if (!record?.tokenId) {
                    return '- -';
                }
                let tokenUuid = '';
                if (token?.includes('#')) {
                    tokenUuid = `#${token?.split('#')[1]}`;
                }
                return (
                    <a
                        onClick={() => {
                            let path = `${location?.pathname}/child?type=${type}&token=${encodeURIComponent(
                                token
                            )}&childToken=${encodeURIComponent(record?.tokenId + tokenUuid)}`;

                            if (versionToken) {
                                path += `&versionToken=${encodeURIComponent(versionToken)}`;
                            }

                            if (searchParams) {
                                path += `&${searchParams}`;
                            }
                            history.push(path);
                        }}>
                        {I18N.childflow.index.chaKanBaoGao}
                    </a>
                );
            }
        });
    }

    return (
        <Spin spinning={runDataInfoLoading}>
            <>
                {cycle?.length + normal?.length < 1 && <Empty type="no-result" />}
                {!!cycle?.length && (
                    <InfoSection>
                        <Descriptions title={I18N.childflow.index.xunHuanZiCeLue} />
                        {cycle?.map((v, i) => {
                            return (
                                <div className="tab-child-flow-table" key={i}>
                                    <Table
                                        title={() => v?.custName || ''}
                                        columns={columns}
                                        rowKey="tokenId"
                                        shadowed={false}
                                        bordered={true}
                                        striped={true}
                                        dataSource={v?.flowDataList || []}
                                        pagination={false}
                                    />
                                </div>
                            );
                        })}
                    </InfoSection>
                )}

                {!!normal?.length && (
                    <InfoSection className="mt10">
                        <Descriptions title={I18N.childflow.index.puTongZiCeLue} />
                        {normal?.map((v, i) => {
                            return (
                                <div className="tab-child-flow-table" key={i}>
                                    <Table
                                        shadowed={false}
                                        bordered={true}
                                        striped={true}
                                        columns={columns}
                                        rowKey="tokenId"
                                        dataSource={v?.flowDataList || []}
                                        pagination={false}
                                    />
                                </div>
                            );
                        })}
                    </InfoSection>
                )}
            </>
        </Spin>
    );
};
