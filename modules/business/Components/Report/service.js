import queryString from 'query-string';
import request from '@/utils/request';

const getUrl = (url, query) => {
    if (query?.secretKey) {
        url += '/bypass';
    }
    if (query?.reportAuth) {
        url += '/open';
    }
    return url + '?' + queryString.stringify(query);
};

/**
 * @description: 获取所有系统字段
 */
const getIndexAndFields = async (param) => {
    return request(
        getUrl('/noahApi/common/variable/select', param),
        {
            method: 'GET'
        },
        true
    );
};

/**
 * @description: 获取处置方式
 */
const getDealTypeList = async (param) => {
    return request(
        getUrl('/noahApi/common/dealtype/select', param),
        {
            method: 'GET'
        },
        true
    );
};

/**
 * @description: 获取报告详情
 */
const getReportDetail = async (param) => {
    return request(
        getUrl('/noahApi/policy/report/test/baseInfo', param),
        {
            method: 'GET'
        },
        true
    );
};

const ReportOpen = async (param) => {
    return request(
        getUrl('/report/open', param),
        {
            method: 'GET'
        },
    );
};

/**
 * @description: 获取当前tab详情
 */
const getRunData = async (param) => {
    return request(
        getUrl('/noahApi/policy/report/runData', param),
        {
            method: 'GET'
        },
        true
    );
};

/**
 * @description: 获取当前tab详情
 */
const getAllCompontlog = async (param) => {
    return request(
        getUrl('/noahApi/policy/report/getAllCompontlog', param),
        {
            method: 'GET'
        },
        true
    );
};

/**
 * @description: 下载报告
 */
const reportDownload = async (param) => {
    return request(
        getUrl('/creditApi/report/download', param),
        {
            method: 'GET'
        },
        true
    );
};

const getComponentTab = (params) => {
    return request(
        getUrl('/noahApi/relationReference/getComponent', params),
        {
            method: 'GET'
        },
        true
    );
};
// 查询节点跳转信息
const getNodeJumpInfo = (params) => {
    return request(
        getUrl('/noahApi/policy/report/nodeJumpInfo', params),
        {
            method: 'GET'
        },
        true
    );
};

export default {
    ReportOpen,
    getIndexAndFields,
    getDealTypeList,
    getReportDetail,
    getRunData,
    getAllCompontlog,
    reportDownload,
    getComponentTab,
    getNodeJumpInfo
};
