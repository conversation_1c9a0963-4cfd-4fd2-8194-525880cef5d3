import I18N from '~I18N';
import { PureComponent } from 'react';
import { Collapse, Icon, Descriptions, Ellipsis } from 'tntd';
import { cloneDeep } from 'lodash';
import { isJSON } from '@tntd/utils';
import { getFieldType, transferValue } from '../../../util';
import './index.less';

const { Panel } = Collapse;
class Table extends PureComponent {
    render() {
        const { data: initData, nodeType, ruleAndIndexFieldMap = [] } = this.props;
        let data = initData;
        data.forEach((item, i) => {
            if (!item.displayName) data.splice(i, 1);
            item.propertyInfo = getFieldType(item);
        });

        if (['ChildFlowNode'].includes(nodeType) && isJSON(data?.[0]?.value)) {
            return (
                <>
                    {data?.map((item, i) => {
                        item = transferValue(cloneDeep(item), ruleAndIndexFieldMap);
                        return (
                            <div key={i}>
                                <div className="child-flow-node-title">
                                    <span style={{ color: item?.propertyInfo?.color || '' }}>{item?.propertyInfo?.typeName || ''}</span>
                                    {item.displayName}
                                </div>
                                <Collapse
                                    bordered={false}
                                    expandIcon={({ isActive }) => <Icon type="caret-right" rotate={isActive ? 90 : 0} />}
                                    className="child-collapse-wrap">
                                    {Array.isArray(item?.value) &&
                                        item?.value?.map((itemTable, itemTableI) => {
                                            const panelTitle = itemTable?.reduce((pre, cur) => {
                                                pre += `${cur?.label}:${cur.value}；`;
                                                return pre;
                                            }, '');

                                            return (
                                                <Panel
                                                    key={itemTableI}
                                                    header={
                                                        <div className="child-panel-title">
                                                            {panelTitle ||
                                                                I18N.template(I18N.table.index.canShuITE, { val1: itemTableI + 1 })}
                                                        </div>
                                                    }>
                                                    <Descriptions className="tntd-description-divider description-item-wrap" column={2}>
                                                        {Array.isArray(itemTable) &&
                                                            itemTable?.map((item, itemI) => {
                                                                let curV = item.value;
                                                                if (typeof curV === 'boolean') {
                                                                    curV = String(curV);
                                                                }
                                                                if (typeof curV === 'number') {
                                                                    curV = String(curV);
                                                                }
                                                                return (
                                                                    <Descriptions.Item key={itemI}>
                                                                        <Ellipsis
                                                                            prefix={
                                                                                <div className="label-title">
                                                                                    <Ellipsis title={item?.label} />
                                                                                </div>
                                                                            }
                                                                            title={curV}
                                                                        />
                                                                    </Descriptions.Item>
                                                                );
                                                            })}
                                                    </Descriptions>
                                                </Panel>
                                            );
                                        })}
                                </Collapse>
                            </div>
                        );
                    })}
                </>
            );
        }
        return (
            <Descriptions className="tntd-description-divider description-item-wrap" column={2}>
                {data?.map((item, index) => {
                    let curV = item.value;
                    if (typeof curV === 'boolean') {
                        curV = String(curV);
                    }
                    if (typeof curV === 'number') {
                        curV = String(curV);
                    }
                    return (
                        <Descriptions.Item colon={false} key={index}>
                            <Ellipsis
                                prefix={
                                    <div className="label-title">
                                        <Ellipsis
                                            prefix={
                                                <span style={{ color: item?.propertyInfo?.color }}>{item?.propertyInfo?.typeName}</span>
                                            }
                                            title={item.displayName}
                                        />
                                    </div>
                                }
                                title={curV}
                            />
                        </Descriptions.Item>
                    );
                })}
            </Descriptions>
        );
    }
}

export default Table;
