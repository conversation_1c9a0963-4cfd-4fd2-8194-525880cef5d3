import history from '../../../../../../website/src/utils/history.js';
import service from '../../../../service';
// 画布中的跳转
const JumpTab = async (componentType, componentId) => {
    //通过查询组织是否是导入待提交从而判断跳转区域是编辑区还是运行区
    const params = {
        componentType,
        componentId
    };
    let data = await service.getComponentTab(params);
    const currentTab = data?.data?.isOnline ? 1 : 2;
    return currentTab;
};

export default {
    ThirdServiceNode: {
        go: (data) => {
            history.push(`/handle/supplierManagement/dataServiceList?name=${data.selectService || ''}`);
        }
    },
    RuleSetServiceNode: {
        go: async (data) => {
            let currentTab = 1;
            if (data.eventId) {
                currentTab = await JumpTab('RULE_SET', data.eventId);
            }
            history.push(`/noah/ruleSet?currentTab=${currentTab}&code=${data.code || ''}`);
        }
    },
    FunctionServiceNode: {
        go: async (data) => {
            let currentTab = 1;
            if (data.code) {
                currentTab = await JumpTab('FUNCTION', data.code);
            }
            history.push(`/noah/formula?currentTab=${currentTab}&code=${(data.code || '').replace(/\[\W*\]/, '')}`);
        }
    },
    DecisionToolServiceNode: {
        go: async (data) => {
            let currentTab = 1;
            if (data.type) {
                currentTab = await JumpTab('DECISION_TOOL', data.type);
            }
            history.push(`/noah/bodyguard/modelTool?currentTab=${currentTab}&code=${data.type || ''}`);
        }
    },
    ScoreCardServiceNode: {
        go: async (data) => {
            let currentTab = 1;
            if (data.scoreCardCode) {
                currentTab = await JumpTab('SCORE_CARD', data.scoreCardCode);
            }
            history.push(`/noah/bodyguard/scoreCard?currentTab=${currentTab}&code=${data.scoreCardCode || ''}`);
        }
    },
    FeatureServiceNode: {
        go: (data) => {
            history.push(`/handle/supplierManagement/dataServiceList??displayName=${data?.name}`);
        }
    },
    ModelServiceNode: {
        go: (data) => {
            history.push(`/model/modelManage?modelUuid=${data?.modelUuid}`);
        }
    },
    ChildFlowNode: {
        go: (data) => {
            history.push(`/noah/policyManage?currentTab=2&code=${data?.policyCode}`);
        }
    }
};
