#!/bin/bash

# Read the peerDependencies from the JSON file using a Node.js script
peerDependencies=$(node -e "console.log(Object.keys(require('./package.json').peerDependencies).join('\n'))")
# Loop through the peerDependencies and install each package with the specified version
for package in $peerDependencies
do
    version=$(node -e "console.log(require('./package.json').peerDependencies['$package'])")
    echo "\nInstalling $package@$version"
    npm install "$package@$version"
done
