import I18N from '~I18N';
import { cloneDeep } from 'lodash';
import { isJSO<PERSON> } from '@/utils/isJSON';

import history from "../../../website/src/utils/history.js";
import service from '../service';

// 非画布的跳转
export const jumpDetail = async (data) => {
    const res = await service.getNodeJumpInfo({
        nodeCode: data?.extension?.code,
        version: data?.extension?.version,
        nodeType: data?.extension?.nodeType
    });
    const nodeType = data?.extension?.nodeType;
    if (res.code === 200) {
        if (nodeType === 'FunctionServiceNode') {
            // 函数库跳转
            history.push(`/noah/formula/detail/view/${res?.data?.nodeVersionUuid}`);
        } else if (nodeType === 'RuleSetServiceNode') {
            // 规则集跳转
            let path = `/noah/ruleSet/rulesConfig?pageName=run&uuid=${res?.data?.nodeVersionUuid}`;
            history.push(path);
        } else if (nodeType === 'ScoreCardServiceNode') {
            // 评分卡跳转
            let path = `/noah/bodyguard/scoreCard/detail?currentTab=1&pageType=view&scoreCardUuid=${res?.data?.nodeUuid}&scoreCardVersionUuid=${res?.data?.nodeVersionUuid}&version=${data?.extension?.version}`;
            history.push(path);
        } else if (nodeType === 'DecisionToolServiceNode') {
            // 决策工具跳转
            history.push(`/noah/bodyguard/modelTool?currentTab=1&code=${data?.extension?.code}`);
        }
    }
};

export const getFieldType = (item) => {
    const { variableType } = item || {};
    if (variableType) {
        if (variableType === 'field') {
            return {
                isZb: false,
                typeName: I18N.reportcontent.util.zi,
                color: '#126BFB'
            };
        }
        return {
            isZb: true,
            typeName: I18N.reportcontent.util.zhi,
            color: '#f6b243'
        };
    }
    return {};
};

export const getFieldByTypeEnum = (type) => {
    if (String(type) === '1') {
        return {
            isZb: false,
            typeName: I18N.reportcontent.util.zi,
            color: '#126BFB'
        };
    }
    return {
        isZb: true,
        typeName: I18N.reportcontent.util.zhi,
        color: '#f6b243'
    };
};

// Tab页面添加Search定位
export function searchToObject(search) {
    let pairs = search.substring(1).split('&');
    let obj = {};
    let pair;
    let i;
    for (i in pairs) {
        if (pairs[i] === '') {
            continue;
        }
        pair = pairs[i].split('=');
        obj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
    }
    return obj;
}

export const transferValue = (item, ruleAndIndexFieldMap) => {
    const { value } = cloneDeep(item);
    if (isJSON(value)) {
        let tempValue = JSON.parse(value);
        if (tempValue?.length) {
            const newV = [];
            tempValue?.map((v) => {
                const newVChild = [];
                for (let key in v) {
                    const ruleIndex = ruleAndIndexFieldMap[key];
                    let newVa = v[key];
                    if (ruleIndex?.type === 'ENUM' && ruleIndex?.enumTypeValues) {
                        newVa =
                            ruleIndex?.enumTypeValues?.find((eum) => {
                                return eum?.value === newVa;
                            })?.description || newVa;
                    }
                    newVChild.push({
                        label: ruleIndex?.dName || key,
                        value: newVa
                    });
                }
                newV.push(newVChild);
            });
            item.value = newV;
        }
    }
    return item;
};

/**
 * 获取有几个大类，层级(包含变量和分箱)
 * @param {*} arr
 * @returns
 */
export const countNestedChildren = (arr) => {
    let maxDepth = 0;

    function getMaxDepth(obj, depth) {
        if (Array.isArray(obj.children) && obj.children.length > 0) {
            for (const child of obj.children) {
                maxDepth = Math.max(maxDepth, depth + 1);
                getMaxDepth(child, depth + 1);
            }
        }
    }

    for (const obj of arr) {
        getMaxDepth(obj, 1);
    }

    if (!isNaN(maxDepth) && maxDepth >= 2) {
        return maxDepth - 1;
    }

    return 2;
};
export const translationStr = (children, logicOperator) => {
    let arr = children?.map((i, index) => index + 1) || [];
    let link = '';

    switch (logicOperator) {
        case '&&':
            link = '&';
            break;
        case '||':
            link = '|';
            break;
        case '!&&':
            link = '&';
            break;
        case '!||':
            link = '|';
            break;
        default:
            null;
            break;
    }
    let checkedStr = arr.join(link);
    return checkedStr;
};
