@import url(../../../../variable.less);
.table-list{
	.ant-table-body{
		// border: 1px solid @border-color;
		// border-radius: @border-radius;
		th,td{
            border:none;
		}
        &.ant-table-empty{
            .ant-table-content{
                display: none;
            }
        }
        ul.rule-hit-info{
            list-style: none;
            padding:0;
            margin:0;
            li{
                padding-left:8px;
                position: relative;
                width: 100%;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                &:not:last-of-type{
                    margin-bottom:8px;
                }
                &::before{
                    content: "";
                    width: 4px;
                    height:4px;
                    background: #D96156;
                    border-radius: 100%;
                    position: absolute;
                    top: 50%;
                    margin-top: -2px;
                    left: 0;
                }
            }
        }
	}
    .simulate-tag{
        color:#D18B21;
        border:1px solid #FFDB87;
    }
    .formal-tag{
        color:#126BFB;
        border:1px solid #8CC4FF;
    }
}
.noDisturb-tag {
    background: #d5f4e8;
    border: 1px solid #07C790;
    color: #07C790;
}
