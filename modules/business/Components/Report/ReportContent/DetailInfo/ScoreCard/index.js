import React, { memo } from 'react';
import ScoreCardItem from './ScoreCardItem';
import './index.less';

const ScoreCard = ({ runDataInfo, allMap: { ruleAndIndexFieldMap, ruleAndIndexFieldList }, ...rest }) => {
    if (!Array.isArray(runDataInfo) || !runDataInfo.length) {
        return null;
    }

    return (
        <div className="scorecard-detail-warpper report-one-part">
            {runDataInfo.map((i, index) => {
                return <ScoreCardItem key={index} {...{ detail: i, allMap: { ruleAndIndexFieldMap, ruleAndIndexFieldList, ...rest } }} />;
            })}
        </div>
    );
};

export default memo(ScoreCard);
