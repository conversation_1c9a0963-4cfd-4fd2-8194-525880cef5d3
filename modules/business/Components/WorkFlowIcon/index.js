import Action from './lib/Action';
import ChampionChallenger from './lib/ChampionChallenger';
import ContinueSupplement from './lib/ContinueSupplement';
import DecisionTool from './lib/DecisionTool';
import End from './lib/End';
import Function from './lib/Function';
import Judgment from './lib/Judgment';
import Model from './lib/Model';
import Parallel from './lib/Parallel';
import ProcessTemplate from './lib/ProcessTemplate';
import Ruleset from './lib/Ruleset';
import ScoreCard from './lib/ScoreCard';
import Start from './lib/Start';
import Strategy from './lib/Strategy';
import SubStrategy from './lib/SubStrategy';
import ThirdPartyService from './lib/ThirdPartyService';

const componentMap = {
    action: Action,
    championchallenger: ChampionChallenger,
    continuesupplement: ContinueSupplement,
    decisiontool: DecisionTool,
    end: End,
    function: Function,
    judgment: Judgment,
    model: Model,
    parallel: Parallel,
    processtemplate: ProcessTemplate,
    ruleset: Ruleset,
    scorecard: ScoreCard,
    start: Start,
    strategy: Strategy,
    substrategy: SubStrategy,
    thirdpartyservice: ThirdPartyService
};

const WorkFlowIcon = (props) => {
    const { type } = props;

    const SelectedComponent = componentMap[type];

    const className = 'first-menu-icon first-menu-icon-' + type;

    return SelectedComponent ? (
        <SelectedComponent className={className} {...props} />
    ) : (
        <Start className={'first-menu-icon first-menu-icon-start'} {...props} />
    );
};

export default WorkFlowIcon;
