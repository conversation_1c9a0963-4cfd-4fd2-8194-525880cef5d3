import { cloneDeep } from 'lodash';
import queryString from 'query-string';
import { parseQueryString, trim } from '@tntd/utils';
import { isJSON } from '@/utils/isJSON';

export function getUrlKey(name) {
    return (
        decodeURIComponent((new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`).exec(location.href) || ['', ''])[1].replace(/\+/g, '%20')) || null
    );
}

/**
 * @description: 报告对象数组去重
 */
export function unique(array) {
    let result = {};
    let finalResult = [];

    for (let i = 0; i < array.length; i++) {
        result[array[i].id] = array[i];
    }
    for (let item in result) {
        finalResult.push(result[item]);
    }
    return finalResult;
}

/**
 *
 * @desc 随机生成颜色
 * @return {String}
 */
export function randomColor() {
    return '#' + ('00000' + ((Math.random() * 0x1000000) << 0).toString(16)).slice(-6);
}

// Tab页面添加Search定位
export function searchToObject(search) {
    let pairs = search.substring(1).split('&');
    let obj = {};
    let pair;
    let i;
    for (i in pairs) {
        if (pairs[i] === '') {
            continue;
        }
        pair = pairs[i].split('=');
        obj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
    }
    return obj;
}

// JS判断字符串长度（英文占1个字符，中文汉字占2个字符）
export function getStrLen(str) {
    let len = 0;
    for (let i = 0; i < str.length; i++) {
        let c = str.charCodeAt(i);
        // 单字节加1
        if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
            len++;
        } else {
            len += 2;
        }
    }
    return len;
}

// 获取文件名，不含后缀
export function splitFileName(text) {
    var pattern = /\.{1}[a-z]{1,}$/;
    if (pattern.exec(text) !== null) {
        return text.slice(0, pattern.exec(text).index);
    }
    return text;
}

export function flatten(arr) {
    var res = [];
    for (let i = 0, length = arr.length; i < length; i++) {
        if (Array.isArray(arr[i])) {
            res = res.concat(flatten(arr[i]));
        } else {
            res.push(arr[i]);
        }
    }
    return res;
}
export const traverseTree = (treeData, callback, pnode, pnodes = []) => {
    (treeData || []).every((node, index) => {
        let result;
        if (callback) {
            result = callback(node, pnode, pnodes?.length ? pnodes : [pnode].filter((item) => !!item), index);
        }
        // 回调函数返回false则终止遍历
        if (result !== false) {
            node && traverseTree(node.children || [], callback, node, [node, ...pnodes]);
        }

        return result !== false;
    });
    return treeData;
};

export const getFieldType = (item) => {
    const { variableType } = item || {};
    if (variableType) {
        if (variableType === 'field') {
            return {
                isZb: false,
                typeName: '[字]',
                color: '#126BFB'
            };
        }
        return {
            isZb: true,
            typeName: '[指]',
            color: '#f6b243'
        };
    }
    return {};
};

export const getFieldByTypeEnum = (type) => {
    if (String(type) === '1') {
        return {
            isZb: false,
            typeName: '[字]',
            color: '#126BFB'
        };
    }
    return {
        isZb: true,
        typeName: '[指]',
        color: '#f6b243'
    };
};

export const transferValue = (item, ruleAndIndexFieldMap) => {
    const { value } = cloneDeep(item);
    if (isJSON(value)) {
        let tempValue = JSON.parse(value);
        if (tempValue?.length) {
            const newV = [];
            tempValue?.map((v) => {
                const newVChild = [];
                for (let key in v) {
                    const ruleIndex = ruleAndIndexFieldMap[key];
                    let newVa = v[key];
                    if (ruleIndex?.type === 'ENUM' && ruleIndex?.enumTypeValues) {
                        newVa =
                            ruleIndex?.enumTypeValues?.find((eum) => {
                                return eum?.value === newVa;
                            })?.description || newVa;
                    }
                    newVChild.push({
                        label: ruleIndex?.dName || key,
                        value: newVa
                    });
                }
                newV.push(newVChild);
            });
            item.value = newV;
        }
    }
    return item;
};

// 入参去除首尾空格
export function trimParams(type, params) {
    if (type === 'url') {
        let url = params.split('?')[0];
        let obj = parseQueryString(params);
        for (let k in obj) {
            obj[k] = trim(obj[k]);
        }
        return url + '?' + queryString.stringify(obj);
    }
    let obj = params;
    for (let k in obj) {
        obj[k] = trim(obj[k]);
    }
    return obj;
}
