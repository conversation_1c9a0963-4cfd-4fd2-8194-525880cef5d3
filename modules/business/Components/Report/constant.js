import I18N from '~I18N';
export const nodeTypeMap = {
    ModelServiceNode: {
        label: I18N.report.constant.moXingZhiXingXin,
        dName: I18N.report.constant.moXingMingCheng,
        key: 2
    },
    ChildFlowNode: {
        label: I18N.report.constant.ziCeLueZhiXing,
        dName: I18N.report.constant.ziCeLueMingCheng,
        key: 8
    },
    BaseInfo: {
        label: I18N.report.constant.xinXiZongLan,
        dName: I18N.report.constant.xinXiZongLan,
        key: 0
    },
    RuleSetServiceNode: {
        label: I18N.report.constant.guiZeZhiXingXin,
        dName: I18N.report.constant.guiZeJiMingCheng,
        key: 1
    },
    FunctionServiceNode: {
        label: I18N.report.constant.hanShuKuZhiXing,
        dName: I18N.report.constant.hanShuKuMingCheng,
        key: 5
    },
    IndexServiceNode: {
        label: I18N.report.constant.zhiBiaoXinXi,
        dName: I18N.report.constant.zhiBiaoMingCheng,
        key: 6
    },
    ThirdServiceNode: {
        label: I18N.report.constant.sanFangZhiXingXin,
        dName: I18N.report.constant.sanFangMingCheng,
        key: 4
    },
    FeatureServiceNode: {
        label: I18N.report.constant.sanFangZhiXingXin,
        dName: I18N.report.constant.waiShuZhiBiaoMing,
        key: 7
    },
    ScoreCardServiceNode: {
        label: I18N.report.constant.pingFenKaZhiXing,
        dName: I18N.report.constant.pingFenKaMingCheng,
        key: 9
    },
    DecisionToolServiceNode: {
        label: I18N.report.constant.jueCeGongJuZhi,
        dName: I18N.report.constant.jueCeGongJuMing,
        key: 3
    },
    SubDecisionFlowNode: {
        label: '流程模板',
        dName: '流程模板名称',
        key: 10
    }
};

export const NodeNameMap = {
    StartFlowNode: I18N.mmeditor.dataconvert.kaiShi,
    EndFlowNode: I18N.mmeditor.dataconvert.jieShu,
    ParallelGateway: I18N.mmeditor.dataconvert.bingXing2,
    ExclusiveGateway: I18N.mmeditor.dataconvert.panDuan,
    SubDecisionFlowNode: '流程模板' ,
    ChildFlowNode: I18N.report.constant.ziCeLue,
    RuleSetServiceNode: I18N.mmeditor.dataconvert.guiZeJi,
    ThirdServiceNode: I18N.report.constant.sanFang,
    FeatureServiceNode: I18N.report.constant.sanFang,
    DecisionToolServiceNode: I18N.mmeditor.dataconvert.jueCeGongJu,
    ModelServiceNode: I18N.mmeditor.dataconvert.moXing,
    FunctionServiceNode: I18N.report.constant.hanShuKu,
    ChampionServiceNode: I18N.mmeditor.dataconvert.guanJunTiaoZhanZhe2,
    ActionServiceNode: I18N.mmeditor.dataconvert.dongZuo,
    ScoreCardServiceNode: I18N.scorecard.scorecarditem.pingFenKa,
    SuspendFlowNode: I18N.mmeditor.dataconvert.jiXuBuChong
};
