const path = require('path');
const webpack = require('webpack');
const merge = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const baseWebpackConfig = require('./webpack.base.conf');
const config = require('./config');

const projectCode = 'modules'; // 对应应用的标识

module.exports = merge(baseWebpackConfig, {
    mode: 'development',
    cache: true,
    output: {
        chunkFilename: config.common.sourcePrefix + 'js/[name].js',
        publicPath: config.dev.assetsPublicPath,
        library: projectCode,
        libraryTarget: 'umd',
        chunkLoadingGlobal: `webpackJsonp_${projectCode}`
    },
    devServer: {
        host: config.dev.host,
        port: config.dev.port,
        static: [path.join(__dirname, '../public')],
        open: config.dev.autoOpenBrowser,
        proxy: config.dev.proxyTable || {},
        webSocketServer: false,
        historyApiFallback: {
            rewrites: [
                {
                    from: /!^\/api/g,
                    to: '/'
                }
            ]
        },
        allowedHosts: 'all'
    },
    devtool: config.dev.devtool,
    plugins: [
        new webpack.HotModuleReplacementPlugin(),
        new HtmlWebpackPlugin({
            filename: 'index.html',
            template: config.common.htmlTemplatePath, // 配置html模板的地址
            inject: true,
            chunksSortMode: 'none'
        })
    ],
    optimization: {
        minimize: false,
        moduleIds: 'named'
    }
});
