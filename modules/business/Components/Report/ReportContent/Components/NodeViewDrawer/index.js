/*
 * @Author: l<PERSON><PERSON>
 * @CreatDate: 2019-03-01 11:30:17
 * @Describe: 进件报告-流程图-抽屉
 */

import I18N from '~I18N';
import { Tag, TntdIcon as Icon, Ellipsis, Empty, Descriptions, Drawer } from 'tntd';
// import DrawerWrap from '~modules/DrawerWrap';
import NodeParams from './NodeParams';
import InfoSection, { InfoTitle } from '../InfoSection';
import './index.less';

export default (props) => {
    const { data, activeNode = {}, visible, onClose, ruleAndIndexFieldMap } = props;
    const { flowModelinAndOutputParams = [] } = data || {};
    const item = flowModelinAndOutputParams?.find((item) => item.nodeId === activeNode.uuid);
    const {
        nodeInputList = [],
        nodeOutputList: nodeOutputAllList = [],
        nodeType,
        nodeName,
        nodeCost,
        passSuccess,
        extension = {}
    } = item || {};

    const { version } = extension || {};

    // 后端把出参信息和外数指标信息都放在nodeOutputList，需要前端根据variableType为index区分外数
    const [nodeOutputList, nodeExtraList] = [[], []];
    nodeOutputAllList.forEach((output) => {
        if (output?.variableType === 'index') {
            nodeExtraList.push(output);
        } else {
            nodeOutputList.push(output);
        }
    });

    // 策略集、三方和冠军挑战者出参要分组，默认字段还是新加的
    const defaultOutput = [];
    const newOutput = [];
    nodeOutputList.forEach((item) => {
        if (!item.mapping) {
            defaultOutput.push(item);
        } else {
            newOutput.push(item);
        }
    });

    const curNodeName = activeNode?.data?.name;
    const nodeParamsList = nodeInputList.length !== 0 || nodeOutputList.length !== 0 || nodeExtraList?.length !== 0;

    return (
        <Drawer
            width="620px"
            visible={visible}
            className="report-flow-drawer"
            title={
                <div>
                    <Ellipsis
                        prefix={
                            version && (
                                <Tag
                                    color="purple"
                                    style={{ background: 'rgba(148,95,185,0.10)', border: '1px solid rgba(148,95,185,0.4)' }}>
                                    V{version}
                                </Tag>
                            )
                        }
                        title={nodeName}
                    />
                    {nodeType === 'RuleSetServiceNode' && nodeName !== curNodeName && (
                        <div className="red-warn">{I18N.popover.index.luYouMoShiXia}</div>
                    )}
                </div>
            }
            showFooter={!!(nodeCost || nodeCost === 0)}
            footer={
                !!(nodeCost || nodeCost === 0) && (
                    <div className="cost-time">
                        <Icon
                            type="clock"
                            style={{
                                fontSize: '18px',
                                verticalAlign: 'bottom',
                                marginRight: '5px'
                            }}
                        />
                        {I18N.popover.index.haoShi}
                        {nodeCost}ms
                    </div>
                )
            }
            onClose={onClose}>
            <div className="node-detail-wrap">
                {!passSuccess && (nodeParamsList || version) && (
                    <>
                        {/* 入参 */}
                        {nodeInputList?.length !== 0 && (
                            <InfoSection className="node-info-wrap">
                                <Descriptions title={I18N.popover.index.ruCanXinXi} />
                                <NodeParams
                                    ruleAndIndexFieldMap={ruleAndIndexFieldMap}
                                    popover={true}
                                    data={nodeInputList}
                                    nodeType={nodeType}
                                />
                            </InfoSection>
                        )}

                        {/* 出参 */}
                        {nodeOutputList.length !== 0 && (
                            <InfoSection className="mt20 node-info-wrap">
                                <Descriptions title={I18N.popover.index.chuCanXinXi} />
                                {defaultOutput.length > 0 && (
                                    <>
                                        {!['ChildFlowNode'].includes(nodeType) && (
                                            <div style={{ padding: '4px 0px' }}>
                                                <Tag color="#108ee9">{I18N.popover.index.moRenChuCan}</Tag>
                                            </div>
                                        )}
                                        <NodeParams
                                            ruleAndIndexFieldMap={ruleAndIndexFieldMap}
                                            popover={true}
                                            data={defaultOutput}
                                            nodeType={nodeType}
                                        />
                                    </>
                                )}
                                {newOutput.length > 0 && (
                                    <>
                                        <div style={{ padding: '4px 0px', marginTop: '8px' }}>
                                            <Tag color="#FF9800">{I18N.popover.index.yingSheChuCan}</Tag>
                                        </div>
                                        <NodeParams
                                            ruleAndIndexFieldMap={ruleAndIndexFieldMap}
                                            popover={true}
                                            data={newOutput}
                                            nodeType={nodeType}
                                        />
                                    </>
                                )}
                            </InfoSection>
                        )}

                        {/* 外数 */}
                        {!!nodeExtraList?.length && (
                            <InfoSection className="mt20 node-info-wrap">
                                <Descriptions title={I18N.popover.index.waiShuZhiBiaoXin} />
                                <NodeParams
                                    ruleAndIndexFieldMap={ruleAndIndexFieldMap}
                                    popover={true}
                                    data={nodeExtraList}
                                    nodeType={nodeType}
                                />
                            </InfoSection>
                        )}
                    </>
                )}
                {!passSuccess && !nodeParamsList && <Empty type="no-result" imageStyle={{ marginTop: '60px' }} />}
                {passSuccess && <div className="abnormal-tip">{I18N.popover.index.yiChangJianTiaoGuo}</div>}
            </div>
        </Drawer>
    );
};
