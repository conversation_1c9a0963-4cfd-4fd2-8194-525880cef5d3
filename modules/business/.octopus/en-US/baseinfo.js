export default {
  index: {
    ceLueMingCheng: 'Strategy Name:',
    ceLueZiLiuCheng: 'Strategy Sub-Process:',
    ceLueMingCheng2: 'Strategy Name',
    ceLueSuoYouJie:
      '(Summary of results returned by all nodes of the Strategy, including results returned by pre-service and decision complement service)',
    chuCanXinXi: 'Return result',
    qianZhiFuWuZhong:
      '(Request parameter in pre-service, including decision complement service request parameter)',
    ruCanXinXi: 'Request parameter',
  },
};
