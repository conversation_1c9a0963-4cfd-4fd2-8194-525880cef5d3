const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const autoprefixer = require('autoprefixer');

const config = require('./config');
const utils = require('./utils');
const devMode = process.env.SYS_ENV !== 'production';

module.exports = {
    cache: true,
    context: path.resolve(__dirname, '../'),
    entry: {
        app: './src/app.js'
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env': JSON.stringify({
                PORT: process.env.PORT,
                SYS_ENV: process.env.SYS_ENV,
                BABEL_ENV: process.env.BABEL_ENV
            })
        }),
        new webpack.ProvidePlugin({
            React: 'react'
        }),
        new webpack.HotModuleReplacementPlugin()
    ],
    output: {
        path: config.build.assetsRoot,
        filename: config.common.sourcePrefix + '/[name].[chunkhash].js'
    },
    resolve: {
        extensions: ['.js', '.json'],
        alias: {
            '@': path.resolve(__dirname, '../../website/src'),
            '~modules': path.resolve(__dirname, '../../Components'),
            '~I18N': path.resolve(__dirname, '../../I18N.js'),
            tntd: 'tntd-v3'
        }
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /(node_modules)/,
                use: {
                    loader: 'babel-loader?cacheDirectory=true'
                }
            },
            {
                test: /\.css$/,
                use: [devMode ? 'style-loader' : MiniCssExtractPlugin.loader, 'css-loader']
            },
            {
                test: /\.less$/,
                use: [
                    devMode ? 'style-loader' : MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            modules: 'global'
                        }
                    },
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [autoprefixer()]
                            }
                        }
                    },
                    {
                        loader: 'less-loader',
                        options: {
                            lessOptions: {
                                javascriptEnabled: true,
                                modules: true,
                                localIndexName: '[name]__[local]___[chunkhash:base64:5]',
                                modifyVars: {
                                    hack: 'true; @import "~tntd/themes/default/variables.less";'
                                }
                            }
                        }
                    }
                ]
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: utils.assetsPath('img/[name].[chunkhash:7][ext]')
                }
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: utils.assetsPath('media/[name].[chunkhash:7][ext]')
                }
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: utils.assetsPath('fonts/[name].[chunkhash:7][ext]')
                }
            },
            {
                test: /\.(txt|jst)$/,
                loader: 'raw-loader'
            }
        ]
    }
};
