// import I18N from '~I18N';
import { useEffect, useState } from 'react';
import { Table, Tooltip, Spin, Empty, Ellipsis, Tag, Descriptions, Icon, TntdModal } from 'tntd';
import WorkflowChart from '../../Components/WorkflowChart';
import { clone, cloneDeep } from 'lodash';
// import history from '../../../../../website/src/utils/history.js';
import { searchToObject } from '@/utils/utils';
import history from '@/utils/history';
import InfoSection, { InfoTitle } from '../../Components/InfoSection';
import './index.less';
export default (props) => {
    const { runDataInfo, runDataInfoLoading, defaultToken, data, flowLogData } = props;
    const [subflowData, setSubflowData] = useState({});
    // 是否弹窗展示子策略以及全屏
    const [fullscreen, setFullscreen] = useState(false);
    const [visible, setVisible] = useState(false);
    const [newflowLogData, setNewFlowLogData] = useState({});
    // 初始化子流程
    const initSubflowData = () => {
        setSubflowData({
            graphJson: [],
            visible: false
        });
    };
    useEffect(() => {}, []);
    const getData = (record) => {
        let graphJson = JSON.parse(data.workflowVersionDTO.graphJson);
        let datra = graphJson.flowNodeDefinitions.find((v) => v.id === record.nodeId);
        let newData = cloneDeep(flowLogData);
        //改造新的flowLogData
        newData.flowModelinAndOutputParams = flowLogData?.flowModelinAndOutputParams.find((v) => v.nodeId === record.nodeId).result;
        let newMap = {};
        newData.flowModelinAndOutputParams.map((v) => {
            v.nodeInputList.map((item) => {
                if (newMap[item?.name]?.nodeIdList) {
                    newMap[item?.name]?.nodeIdList.push(v.nodeId);
                } else {
                    newMap[item?.name] = {
                        ...item,
                        nodeIdList: [v.nodeId]
                    };
                }
            });
            v.nodeOutputList.map((item) => {
                if (newMap[item?.name]?.nodeIdList) {
                    newMap[item?.name]?.nodeIdList.push(v.nodeId);
                } else {
                    newMap[item?.name] = {
                        ...item,
                        nodeIdList: [v.nodeId]
                    };
                }
            });
        });
        newData.fieldMap = newMap;
        newData.diagramLine = record?.diagramLine;
        setNewFlowLogData(newData);

        setSubflowData({ graphJson: datra, visible: true });
    };
    const columns = [
        {
            title: '流程模版',
            width: '50%',
            dataIndex: 'displayName',
            ellipsis: true,
            render: (value, record) => {
                value = (value || '') + (record?.policyCode ? `(${record?.policyCode || ''})` : '');
                const { extension, reasonMessage, reasonCode } = record || {};
                const { version } = extension || {};
                return (
                    <Ellipsis
                        className="ellipsis-align-center"
                        widthLimit="100%"
                        prefix={
                            version && (
                                <Tag
                                    color="purple"
                                    style={{
                                        background: 'rgba(148,95,185,0.10)',
                                        border: '1px solid rgba(148,95,185,0.4)'
                                    }}>
                                    V{version}
                                </Tag>
                            )
                        }
                        suffix={
                            reasonCode !== 200 &&
                            !!reasonMessage && (
                                <Tooltip title={reasonMessage}>
                                    <Icon type="info-circle" />
                                </Tooltip>
                            )
                        }
                        title={value}
                    />
                );
            }
        },
        // {
        //     title: ' ',
        //     width: '30%',
        //     dataIndex: 'wuxinhao'
        // },
        {
            title: '操作',
            width: '20%',
            ellipsis: true,
            render: (record) => {
                return (
                    <a
                        onClick={() => {
                            // const path = '/handle/supplierManagement/dataServiceList';
                            // history.push(path);
                            getData(record);
                            setVisible(true);
                        }}>
                        {'查看模版'}
                    </a>
                );
            }
        }
    ];

    return (
        <Spin spinning={runDataInfoLoading}>
            <>
                <InfoSection>
                    <Descriptions title={'流程模版执行信息'} />

                    <div className="tab-child-flow-table">
                        <Table
                            columns={columns}
                            rowKey="tokenId"
                            shadowed={false}
                            bordered={true}
                            striped={true}
                            dataSource={runDataInfo || []}
                            pagination={false}
                        />
                    </div>
                </InfoSection>
                <TntdModal
                    title={'流程模板'}
                    className="process-info-modal"
                    // className="full-modal"
                    visible={visible}
                    fullscreen={fullscreen}
                    onCancel={() => {
                        setVisible(false);
                        setFullscreen(false);
                        initSubflowData();
                    }}>
                    {!!visible && (
                        <WorkflowChart
                            {...props}
                            getFlowLog={false}
                            setSubflowData={setSubflowData}
                            graphJson={subflowData?.graphJson}
                            tokenId={defaultToken}
                            flowLogData={newflowLogData}
                        />
                    )}
                </TntdModal>
            </>
        </Spin>
    );
};
