@import url(../../../../variable.less);
.report-one-part-item-header{
    background: linear-gradient(180deg, #E7F1FE 0%, rgba(243, 246, 253, 0.84) 100%);
    padding: 16px;
    border-top-left-radius: @border-radius;
    border-top-right-radius: @border-radius;
    &.one-label{
        .item-report-header-item{
            .left-label{
                margin-right: 8px;
            }
        }
    }
    .item-report-header-item{
        margin-bottom: 4px;
        .left-label{
            margin-right: 20px;
            .item-report-header-label{
                color: #454F64;
                display: flex;
                align-items: center;
                line-height: 22px;
                &:not(:last-of-type){
                    margin-bottom: 4px;
                }
                .item-report-header-icon{
                    width: 16px;
                    height: 16px;
                    display: inline-block;
                    background-size: cover;
                    margin-right: 4px;
                    &.item-report-header-name{
                        background-image: url(./images/name.svg);
                    }
                    &.item-report-header-alarm{
                        background-image: url(./images/alarm.svg);
                    }
                    &.item-report-header-result{
                        width: 14px;
                        height: 14px;
                        background-image: url(./images/result.svg);
                    }
                }
            }
        }
        .right-value{
            flex:1;
            overflow: hidden;
            >*{
                line-height: 22px;
                width: 100%;
                .overflow{
                    font-weight: 500;
                }
                &:not(:last-of-type){
                    margin-bottom: 4px;

                }
            }
        }
    }
    + *{
        margin-top:-6px !important;
    }
}
