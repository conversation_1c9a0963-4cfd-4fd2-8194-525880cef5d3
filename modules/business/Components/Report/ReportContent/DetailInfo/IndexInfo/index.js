import I18N from '~I18N';
import { Table, Tooltip } from 'tntd';
import { useState, useEffect } from 'react';

export default (props) => {
    const { runDataInfo, runDataInfoLoading } = props;
    const [dataSource, setDataSource] = useState();

    useEffect(() => {
        if (runDataInfo && !runDataInfoLoading) {
            const dataSourceTemp = runDataInfo?.reduce((preArr, cur) => {
                return preArr.concat(
                    cur?.featureList?.map((v) => ({
                        ...v,
                        nodeName: cur.displayName,
                        rowSpan: cur?.featureList?.length
                    })) || []
                );
            }, []);
            setDataSource(dataSourceTemp);
        }
    }, [runDataInfo, runDataInfoLoading]);

    const columns = [
        {
            title: I18N.indexinfo.index.zhiBiaoMingCheng,
            width: '50%',
            dataIndex: 'displayName',
            ellipsis: true,
            render: (value, record) => {
                return (
                    <Tooltip title={value} placement="topLeft">
                        {value || record?.name}
                    </Tooltip>
                );
            }
        },
        {
            title: I18N.indexinfo.index.zhiBiaoLeiXing,
            width: '20%',
            dataIndex: 'indexTypeName',
            ellipsis: true,
            render: (value) => {
                return (
                    <Tooltip title={value} placement="topLeft">
                        {value}
                    </Tooltip>
                );
            }
        },
        {
            title: I18N.indexinfo.index.zhiBiaoZhi,
            width: '30%',
            dataIndex: 'value',
            ellipsis: true,
            render: (value) => {
                return (
                    <Tooltip title={value} placement="topLeft">
                        {value}
                    </Tooltip>
                );
            }
        }
    ];
    return (
        <Table
            shadowed={false}
            bordered={true}
            loading={runDataInfoLoading}
            columns={columns}
            rowKey="name"
            dataSource={dataSource}
            pagination={{ pageSize: 10 }}
        />
    );
};
