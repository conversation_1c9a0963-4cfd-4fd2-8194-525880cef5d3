import I18N from '~I18N';
import { useEffect, useState } from 'react';
import { Spin, Empty, Descriptions } from 'tntd';
import RuleSetTable from './RuleSetTable';
import Header from './Header';
import InfoSection, { InfoDesc } from '../../Components/InfoSection';
import { nodeTypeMap } from '../../../constant';
import { getFieldType } from '../../util';
import './index.less';

const RuleInfo = (props) => {
    const { nodeType, dealTypeList, runDataInfo, runDataInfoLoading, canJump } = props;
    const [nodeName, setNodeName] = useState();
    useEffect(() => {
        if (nodeType) {
            const curNodeName = Object.values(nodeTypeMap).find((item) => item.key === nodeType)?.dName;
            setNodeName(curNodeName);
        }
    }, [nodeType]);
    return (
        <>
            {runDataInfoLoading && <Spin className="globalSpin" style={{ margin: '30px auto' }} />}
            {!runDataInfoLoading && (
                <div className="report-one-part">
                    {runDataInfo?.map((item, index) => {
                        const { extension } = item || {};
                        return (
                            <div className="report-one-part-item" key={index}>
                                <Header canJump={canJump} nodeName={nodeName} nodeType={nodeType} data={item} dealTypeList={dealTypeList} />
                                {String(nodeType) === '1' && (
                                    <RuleSetTable
                                        extension={extension}
                                        dealTypeList={dealTypeList}
                                        ruleSetDisplayName={item?.ruleSetDisplayName}
                                        noDisturb={item?.noDisturb}
                                        riskTag={item?.riskTag}
                                        riskDecision={item?.riskDecision}
                                        riskScore={item?.riskScore}
                                        canJump={canJump}
                                        data={(item?.hitRuleList || []).concat(item?.immunoRuleList || [])}
                                    />
                                )}

                                {!!item?.inputFieldList?.length && (
                                    <>
                                        <Descriptions
                                            className="tntd-description-divider description-item-wrap"
                                            title={I18N.ruleinfo.index.ruCan}>
                                            {item.inputFieldList.map((item1, index) => {
                                                const { typeName, color } = getFieldType(item1);
                                                const labelShow = (
                                                    <>
                                                        <span style={{ color }}>{typeName}</span>
                                                        {item1.displayName}
                                                    </>
                                                );
                                                return (
                                                    <Descriptions.Item colon={false} key={index}>
                                                        <InfoDesc data={{ ...item1, label: labelShow }} />
                                                    </Descriptions.Item>
                                                );
                                            })}
                                        </Descriptions>
                                    </>
                                )}

                                {!!item?.resFieldList?.length && (
                                    <Descriptions
                                        className="tntd-description-divider description-item-wrap"
                                        title={I18N.ruleinfo.index.chuCan}>
                                        {item.resFieldList.map((item1, index) => {
                                            const { typeName, color } = getFieldType(item1);
                                            const labelShow = (
                                                <>
                                                    <span style={{ color }}>{typeName}</span>
                                                    {item1.displayName}
                                                </>
                                            );
                                            return (
                                                <Descriptions.Item colon={false} key={index}>
                                                    <InfoDesc data={{ ...item1, label: labelShow }} />
                                                </Descriptions.Item>
                                            );
                                        })}
                                    </Descriptions>
                                )}

                                {!item?.inputFieldList?.length && !item?.resFieldList?.length && (
                                    <InfoSection>
                                        <Empty type="no-result" imageStyle={{ marginTop: '30px' }} />
                                    </InfoSection>
                                )}
                            </div>
                        );
                    })}
                    {!runDataInfo && <Empty type="no-result" imageStyle={{ marginTop: '30px' }} />}
                </div>
            )}
        </>
    );
};
export default RuleInfo;
