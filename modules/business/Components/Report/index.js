import I18N from '~I18N';
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Spin } from 'tntd';
import ReportWrap from './ReportWrap';
import ReportHeader from './ReportHeader';
import ReportContent from './ReportContent';
import { nodeTypeMap } from './constant';
import service from './service';
import './index.less';

export default forwardRef((props, ref) => {
    const { title, nodesMap, id, isVersion, type, childToken, secretKey, canJump, hideReqDesc = {}, rightExpand } = props || {};
    const { theme = 'blue', subTitle } = props.reportHeader || {};
    const { downMode } = window || {};
    const [loading, setLoading] = useState(!downMode);
    const [ruleAndIndexFieldMap, setRuleAndIndexFieldMap] = useState();
    const [reportList, setReportList] = useState([]);
    const [defaultToken, setDefaultToken] = useState();
    const [reportDetail, setReportDetail] = useState();
    const [dealTypeList, setDealTypeList] = useState();
    const [runDataLoading, setRunDataLoading] = useState(!downMode);
    const [runDataInfo, setRunDataInfo] = useState();
    const [flowLogLoading, setFlowLogLoading] = useState(!downMode);
    const [flowLogData, setFlowLogData] = useState();

    // 下载模式从window上取
    const allMap = downMode ? window.allMap : props?.allMap;
    const isAuth = window.location.pathname.indexOf('/reportAuth') !== -1;

    const [style, setStyle] = useState({
        // height: 'calc(100vh - 90px)'
    });

    useImperativeHandle(ref, () => ({
        reportDetail,
        type
    }));

    // 获取tab信息
    const getRunData = (params) => {
        if (!downMode) {
            setRunDataLoading(true);
            service
                .getRunData({ ...params, reportAuth: isAuth })
                .then((res) => {
                    setRunDataInfo(res?.data);
                })
                .finally(() => {
                    setRunDataLoading(false);
                });
        }
    };

    // 获取报告信息
    const queryDetail = (params) => {
        if (isAuth) {
            return service
                .ReportOpen({
                    type: isVersion && type === '1' ? '2' : type,
                    ...params,
                    secretKey
                    // appCode: allMap?.appCode
                })
                .then((res) => {
                    const { data } = res || {};
                    setReportList(data);
                    setDefaultToken(data?.[0]?.baseResultInfo?.tokenId);

                    let curReportDetail = data?.[0];
                    if (childToken && id) {
                        curReportDetail = data?.find((item) => item?.baseResultInfo?.tokenId === childToken);
                    }
                    setReportDetail(curReportDetail);
                    return res;
                });
        }
        return service
            .getReportDetail({
                type: isVersion && type === '1' ? '2' : type,
                ...params,
                secretKey
            })
            .then((res) => {
                const { data } = res || {};
                setReportList(data);
                setDefaultToken(data?.[0]?.baseResultInfo?.tokenId);

                let curReportDetail = data?.[0];
                if (childToken && id) {
                    curReportDetail = data?.find((item) => item?.baseResultInfo?.tokenId === childToken);
                }
                setReportDetail(curReportDetail);
                return res;
            });
    };

    // 获取轨迹图信息
    const getFlowLog = (tokenId) => {
        if (!downMode) {
            setFlowLogLoading(true);
            return service
                .getAllCompontlog({
                    secretKey,
                    tokenId,
                    type,
                    id: tokenId,
                    reportAuth: isAuth
                })
                .then((res) => {
                    window.allCompontlog = res?.data;
                    setFlowLogData(res?.data);
                    return res?.data;
                })
                .finally(() => {
                    setFlowLogLoading(false);
                });
        }
        setFlowLogLoading(false);
    };

    useEffect(() => {
        debugger;
        if (!downMode) {
            setLoading(true);
            // 获取系统字段
            service.getIndexAndFields({ secretKey, reportAuth: isAuth }).then(({ data }) => {
                const { ruleAndIndexFieldMapObj } =
                    data?.reduce(
                        (total, item) => {
                            const { data, name: sourceKey, dName: sourceName, bizType } = item || {};
                            if (Array.isArray(data)) {
                                data.forEach((i) => {
                                    const { name, ...rest } = i;
                                    total['ruleAndIndexFieldMapObj'][name] = {
                                        ...rest,
                                        name,
                                        sourceName,
                                        sourceKey,
                                        bizType
                                    };
                                });
                            }
                            return total;
                        },
                        {
                            ruleAndIndexFieldMapObj: {}
                        }
                    ) || {};
                setRuleAndIndexFieldMap(ruleAndIndexFieldMapObj);
                window.ruleAndIndexFieldMap = ruleAndIndexFieldMapObj;
            });
            service.getDealTypeList({ secretKey, reportAuth: isAuth }).then((res) => {
                window.dealTypeList = res?.data || [];
                setDealTypeList(res?.data || []);
            });
            queryDetail({ tokenId: id, id, secretKey }).finally(() => {
                setLoading(false);
            });
        } else {
            setStyle({ height: 'calc(100vh)' });
            setReportDetail(window.reportData);
            setDealTypeList(window.dealTypeList);
            setRuleAndIndexFieldMap(window.ruleAndIndexFieldMap);
            setFlowLogData(window.allCompontlog);
        }
    }, [downMode]);

    return (
        <Spin spinning={loading}>
            <ReportWrap
                style={style}
                multiMode={false}
                showRocket={false}
                defaultToken={defaultToken}
                data={reportDetail}
                dataList={reportList}
                onSubjectChange={(curReportInfo) => {
                    setReportDetail(curReportInfo);
                }}>
                <ReportHeader
                    theme={theme}
                    title={title || window.reportTitle}
                    subTitle={
                        subTitle || [
                            {
                                title: I18N.report.index.bianHao,
                                key: 'tokenId'
                            },
                            {
                                title: I18N.report.index.shiJian,
                                key: 'reportTime'
                            }
                        ]
                    }
                    nodeTypeMap={nodesMap || nodeTypeMap}
                    data={reportDetail}
                    dataList={reportList}
                    dealTypeList={dealTypeList}
                    onVersionChange={(tokenId) => {
                        //id:type=1; tokenId:type=2;  token#taskId:type=3
                        let type = '2';
                        if (tokenId.includes('#')) {
                            type = '3';
                        }
                        queryDetail({
                            token: tokenId,
                            id: tokenId,
                            type
                        });
                    }}
                    rightExpand={rightExpand}
                />
                <ReportContent
                    allMap={allMap}
                    hideReqDesc={hideReqDesc}
                    dealTypeList={dealTypeList}
                    ruleAndIndexFieldMap={ruleAndIndexFieldMap}
                    data={reportDetail}
                    defaultToken={defaultToken}
                    nodeDefault={['BaseInfo', 'IndexServiceNode']}
                    nodeTypeMap={nodesMap || nodeTypeMap}
                    runDataInfoLoading={runDataLoading}
                    runDataInfo={runDataInfo}
                    canJump={canJump}
                    onTabChange={(key) => {
                        if (String(key) !== '0') {
                            setRunDataInfo([]);
                            if (downMode) {
                                setRunDataInfo(window.reportData?.nodeRunData?.[key]);
                            } else {
                                getRunData({
                                    secretKey,
                                    nodeType: key,
                                    tokenId: reportDetail?.baseResultInfo?.tokenId,
                                    type,
                                    reportAuth: isAuth
                                });
                            }
                        }
                    }}
                    flowLogLoading={flowLogLoading}
                    flowLogData={flowLogData}
                    getFlowLog={!downMode && getFlowLog}
                />
            </ReportWrap>
        </Spin>
    );
});
