{"name": "business-module", "version": "1.0.0", "description": "1", "main": "index.js", "scripts": {"install:all": "npm install && sh install_peerDep.sh", "start": "nodemon -w website/build -w website/build/mock/index.js website/build/server.js", "build": "webpack --progress --config website/build/webpack.prod.conf.js", "build:report": "webpack --config website/build/webpack.report.runtime.js", "prepare": "husky install", "changeLog": "rm -rf CHANGELOG.md && conventional-changelog -p angular -i CHANGELOG.md -s", "eslint-fixed": "npx eslint --max-warnings 0 --fix --ext .js,.jsx,.ts,.tsx ./modules"}, "repository": {"type": "git", "url": "*********************:app/business-module.git"}, "author": "", "license": "ISC", "peerDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@tntd/drag": "^0.0.1", "@tntd/tooltip-select": "^4.3.2", "@tntd/utils": "^1.1.4", "antd": "^3.26.20", "autoprefixer": "^9.8.8", "babel-loader": "^8.3.0", "babel-plugin-import": "^1.13.8", "core-js": "^3.37.1", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^5.0.1", "dva": "^2.4.1", "html-inline-script-webpack-plugin": "^3.2.1", "html-webpack-plugin": "^5.6.0", "jsencrypt": "^3.3.2", "kiwi-intl": "^1.2.6-beta.0", "less": "^3.13.1", "less-loader": "^11.1.4", "lib-service": "^0.2.28", "lint-staged": "^12.4.1", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.9.0", "mmeditor": "^3.1.5", "moment": "^2.30.1", "nodemon": "^3.1.4", "postcss-loader": "^7.3.4", "query-string": "^6.14.1", "raw-loader": "^4.0.2", "react": "^16.14.0", "react-dom": "^16.14.0", "style-loader": "^3.3.4", "tntd-v3": "npm:tntd@^3.0.0-beta.20", "universal-cookie": "^2.2.0", "webpack": "^5.92.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2", "webpack-merge": "^4.2.2"}, "devDependencies": {"@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "commitizen": "^4.2.4", "conventional-changelog-cli": "^2.2.2", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.3.0", "eslint": "^8.15.0", "eslint-config-tongdun": "^1.1.11", "eslint-plugin-td-rules-plugin": "^1.0.1", "husky": "^8.0.1", "lint-staged": "^12.5.0"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --quiet --fix --ext .js,.jsx,.ts,.tsx"]}, "dependencies": {"@tddc/td-tree-view": "0.0.12", "d3-hierarchy": "^3.1.2"}}