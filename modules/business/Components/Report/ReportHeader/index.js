import I18N from '~I18N';
import { useState, useEffect } from 'react';
import { Row, Col, Dropdown, Menu, Tag, Icon, Ellipsis, Alert } from 'tntd';
import { isJSON } from '@/utils/isJSON';
import './index.less';

const dealStatusMap = {
    '': {
        label: '',
        color: '#5D7092'
    },
    0: {
        label: I18N.reportheader.index.weiChuZhi,
        color: '#5D7092'
    },
    1: {
        label: I18N.reportheader.index.chuZhiZhong,
        color: '#126BFB'
    },
    2: {
        label: I18N.reportheader.index.chuZhiChengGong,
        color: '#07C790'
    },
    3: {
        label: I18N.reportheader.index.chuZhiShiBai,
        color: '#EF6555'
    }
};

export default (props) => {
    const { theme, title, subTitle = [], data, dataList = [], dealTypeList = [], onVersionChange, nodeTypeMap, rightExpand } = props || {};
    const { baseResultInfo = {}, dealResult, dealDescription, dealStatus } = data || {};
    const { baseFieldInfo, tokenId, byPassTokenList, finalDealTypeName, reasonCode } = baseResultInfo || {};
    const [mainByPassTokenList, setMainByPassTokenList] = useState(byPassTokenList);
    const [dealTypeColor, setDealTypeColor] = useState();
    let { errorMessage } = baseResultInfo || {};
    const isAuth = window.location.pathname.indexOf('/reportAuth') !== -1;

    let subErrorMessage;
    let firstErrMsg;
    if (isJSON(errorMessage)) {
        errorMessage = JSON.parse(errorMessage);
        firstErrMsg = errorMessage[0];
        subErrorMessage = errorMessage[0]?.subErrorMessage;
        if (subErrorMessage && isJSON(subErrorMessage)) {
            subErrorMessage = JSON.parse(subErrorMessage);
        }
    }

    useEffect(() => {
        if (byPassTokenList?.length) {
            setMainByPassTokenList(byPassTokenList);
        }
    }, [byPassTokenList]);

    useEffect(() => {
        if (dealTypeList?.length && baseResultInfo) {
            let color = '#d96156';
            dealTypeList?.find((item) => {
                if (item?.dName === finalDealTypeName) {
                    color = item.color;
                }
            });
            setDealTypeColor(color);
        }
    }, [dealTypeList, baseResultInfo]);

    const getDesc = () => {
        return (
            <div className="exception-container">
                {errorMessage && (
                    <>
                        <div className="box-title">
                            {!!dealStatusMap[dealStatus]?.label && (
                                <span style={{ color: dealStatusMap[dealStatus]?.color }}>[{dealStatusMap[dealStatus]?.label}]</span>
                            )}
                            {I18N.reportheader.index.yiChangYuanYin}
                            <span className="exception-content">
                                {typeof errorMessage !== 'string' ? (
                                    <>
                                        {firstErrMsg?.errorCode || reasonCode}；
                                        {(firstErrMsg?.nodeName || '') +
                                            `${nodeTypeMap[firstErrMsg.nodeType]
                                                ? '[' + nodeTypeMap[firstErrMsg.nodeType]?.label + ']'
                                                : ''
                                            }` +
                                            I18N.reportheader.index.zhiXingShiBai +
                                            (firstErrMsg?.errorMessage ? `，${firstErrMsg?.errorMessage}` : '')}
                                    </>
                                ) : (
                                    errorMessage || ''
                                )}
                            </span>
                            {!!subErrorMessage &&
                                subErrorMessage.map((subErr, i) => {
                                    return (
                                        <div key={i} className="sub-exception-content">
                                            <span>{I18N.reportheader.index.ziCeLueYiChang}</span>
                                            {subErr?.errorCode}：{subErr?.nodeName || ''}
                                            {nodeTypeMap[subErr.nodeType] ? `[${nodeTypeMap[subErr.nodeType]?.label}]` : ''}
                                            {I18N.reportheader.index.zhiXingShiBai}
                                            {subErr?.errorMessage ? `，${subErr?.errorMessage}` : ''}
                                        </div>
                                    );
                                })}
                        </div>
                    </>
                )}

                {!!dealResult && (
                    <div className="box-title">
                        {I18N.reportheader.index.zhongZhiJieGuo}{' '}
                        <span className="exception-content">{dealTypeList?.find((item) => item.name === dealResult)?.dName}</span>
                    </div>
                )}

                {!!dealDescription && (
                    <div className="box-title" style={{ marginBottom: '20px' }}>
                        {I18N.reportheader.index.zhongZhiYiJian}
                        <span className="exception-content">{dealDescription}</span>
                    </div>
                )}
            </div>
        );
    };
    return (
        <>
            {(errorMessage || dealResult) && <Alert className="report-warn report-mb10" showIcon message={getDesc()} type="warning" />}
            <div className={`report-head-info-wrap ${theme}`}>
                <div className="top-bg" />
                <div className="report-head-info">
                    <Row type="flex" justify="space-between">
                        <Row type="flex" className="report-head-title" align="middle">
                            <h3>{title || I18N.reportheader.index.guiZeJiCeShi}</h3>
                            {!!mainByPassTokenList?.length && !!dataList?.length && (
                                <Dropdown
                                    overlay={
                                        <Menu
                                            selectedKeys={[tokenId]}
                                            className="switch-report-version"
                                            onClick={({ key }) => {
                                                onVersionChange(key);
                                            }}>
                                            {mainByPassTokenList?.map((passToken, index) => {
                                                const title = `V${passToken.policyVersion}`; // 主版本报告
                                                return (
                                                    <Menu.Item
                                                        key={passToken?.tokenId}
                                                        className={passToken?.tokenId === tokenId ? 'active' : ''}>
                                                        {title}&nbsp;
                                                        {index === 0
                                                            ? I18N.reportheader.index.zhuBanBenBaoGao
                                                            : I18N.reportheader.index.pangLuBaoGao}
                                                    </Menu.Item>
                                                );
                                            })}
                                        </Menu>
                                    }>
                                    {!!dataList?.[0]?.policyVersionDTO?.version && (
                                        <Tag
                                            color="purple"
                                            style={{ background: 'rgba(148,95,185,0.10)', border: '1px solid rgba(148,95,185,0.4)' }}>
                                            V{dataList?.[0]?.policyVersionDTO?.version || '- -'}
                                            <Icon type="down" className="version-icon" />
                                        </Tag>
                                    )}
                                </Dropdown>
                            )}
                            {(!mainByPassTokenList?.length || window?.downMode) && !!data?.policyVersionDTO?.version && (
                                <Tag
                                    color="purple"
                                    style={{ background: 'rgba(148,95,185,0.10)', border: '1px solid rgba(148,95,185,0.4)' }}>
                                    V{data?.policyVersionDTO?.version || '- -'}
                                </Tag>
                            )}
                        </Row>
                    </Row>
                    <div className="report-head-sub-info">
                        {subTitle?.map((v, index) => {
                            return (
                                <span key={index}>
                                    <Ellipsis title={`${v?.title}：${baseResultInfo?.[v?.key] || '- -'}`} />
                                </span>
                            );
                        })}
                    </div>
                    {!!baseResultInfo?.finalDealTypeName && (
                        <div
                            className="report-final-deal"
                            style={{
                                background: dealTypeColor || baseResultInfo?.finalDealTypeColor
                            }}>
                            {I18N.reportheader.index.jueCeJieGuo}
                            <span>{baseResultInfo?.finalDealTypeName}</span>
                        </div>
                    )}
                    <Row className="report-head-person-info" gutter={8}>
                        {baseFieldInfo?.map((item, itemI) => {
                            return (
                                <Col span={6} key={itemI}>
                                    <Ellipsis title={item?.value || '- -'} prefix={<label>{item?.displayName}：</label>} />
                                </Col>
                            );
                        })}
                    </Row>
                </div>
                {rightExpand && !isAuth && <div className="report-head-right-expand">{rightExpand}</div>}
            </div>
        </>
    );
};
