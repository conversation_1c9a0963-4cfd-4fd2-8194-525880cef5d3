:global {
    .child-flow-node-title{
        width: 100%;
        height: 32px;
        line-height: 32px;
    }

    .child-collapse-wrap{
        &.ant-collapse {
            border: none;
            background-color: #fff;
        }
        .child-panel-title{
            padding-left: 4px;
        }
        &.ant-collapse > .ant-collapse-item {
            border-bottom: 0;
            margin-bottom: 8px;
            > .ant-collapse-header{
                padding-top: 12px !important;
                padding-bottom: 12px !important;
                padding-left: 30px !important;
                background-color: #F8F9FB;
                .ant-collapse-arrow{
                    transform: none;
                    top:16px;
                }
            }
            > .ant-collapse-content {
                border:none !important;
                background-color: #F8F9FB;
                > .ant-collapse-content-box{
                    padding-top: 0;
                    .description-item-wrap{
                        padding: 8px;
                    }
                }
            }
        }
    }
    .label-title{
        &>.tntd-ellipsis>.min-width-0{
            flex:1;
        }
    }
}
