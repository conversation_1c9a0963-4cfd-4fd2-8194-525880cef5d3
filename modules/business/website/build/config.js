const path = require('path');
const assetsPublicPath = '/';
const PORT = process.env.PORT || 1234;
const sourcePrefix = 'modules-resource/';
const publicPath = '/modules-resource/';

// const proxyUrl = 'http://10.59.207.29:8088/'; //'https://tiangong.tcloud.tongdun.cn/'; //
// const proxyUrl = 'http://10.59.222.67:8088/';
const proxyUrl = 'http://10.58.16.85:8000/';

module.exports = {
    common: {
        htmlTemplatePath: path.resolve(__dirname, '../src/index.ejs'),
        sourcePrefix
    },
    dev: {
        mock: true,
        hot: true,
        assetsSubDirectory: sourcePrefix + '/static',
        assetsPublicPath: '/',
        proxyTable: {
            '/bridgeApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {}
            },
            '/creditApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {}
            },
            '/noahApi': {
                target: proxyUrl,
                changeOrigin: true,
                pathRewrite: {}
            }
        },
        host: 'localhost',
        port: PORT,
        autoOpenBrowser: true,
        devtool: 'eval-source-map',
        publicPath
    },
    build: {
        assetsRoot: path.resolve(__dirname, '../dist'),
        assetsSubDirectory: sourcePrefix + '/static',
        assetsPublicPath,
        devtool: 'source-map'
    }
};
