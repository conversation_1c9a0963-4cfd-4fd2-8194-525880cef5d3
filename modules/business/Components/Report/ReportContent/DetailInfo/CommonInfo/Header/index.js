import I18N from '~I18N';
import { Row, Tag, Ellipsis, Icon } from 'tntd';
import { jumpDetail } from '../../../util';
import './index.less';

export default (props) => {
    const { dealTypeList, nodeType, nodeName, data = {}, canJump } = props || {};
    const { extension, ruleSetDisplayName, displayName, noDisturb, riskTag, riskDecision } = data || {};
    const { version } = extension || {};

    const getColor = (label) => {
        let color;
        dealTypeList?.find((item) => {
            if (item.dName === label) {
                color = item.color;
            }
        });
        return color;
    };

    const color = riskDecision ? getColor(riskDecision) : '';
    return (
        <div
            className={`report-one-part-item-header ${String(nodeType) === '1' ? '' : 'one-label'}`}
            style={color && String(nodeType) === '1' ? { borderTop: `2px solid ${color}` } : {}}>
            <Row type="flex" align="middle" className="item-report-header-item">
                {/* 左侧label名称 */}
                <Row className="left-label">
                    <div className="item-report-header-label">
                        <i className="item-report-header-icon item-report-header-name" />
                        <label>{nodeName || I18N.rulesettable.index.guiZeJiMingCheng || I18N.ruleinfo.index.mingCheng}</label>
                    </div>
                    {String(nodeType) === '1' && (
                        <>
                            <div className="item-report-header-label">
                                <i className="item-report-header-icon item-report-header-alarm" />
                                <label>{I18N.rulesettable.index.yuJingZhuTi}</label>
                            </div>
                            <div className="item-report-header-label">
                                <i className="item-report-header-icon item-report-header-result" />
                                <label>{I18N.rulesettable.index.fengXianJieGuo}</label>
                            </div>
                        </>
                    )}
                </Row>
                {/* 右侧值 */}
                <Row className="right-value">
                    <Ellipsis
                        prefix={
                            <>
                                {!!version && (
                                    <Tag
                                        color="purple"
                                        style={{ background: 'rgba(148,95,185,0.10)', border: '1px solid rgba(148,95,185,0.4)' }}>
                                        V{version}
                                    </Tag>
                                )}
                                {noDisturb && <Tag className="noDisturb-tag">{I18N.rulesettable.index.mianDaRao}</Tag>}
                            </>
                        }
                        title={String(nodeType) === '1' ? ruleSetDisplayName || '- -' : displayName}
                        suffix={
                            // 2307sp 只支持这四个节点类型的跳转
                            canJump &&
                            ['FunctionServiceNode', 'RuleSetServiceNode', 'ScoreCardServiceNode', 'DecisionToolServiceNode'].includes(
                                data?.extension?.nodeType
                            ) && (
                                <a
                                    style={{ marginLeft: '4px' }}
                                    onClick={() => {
                                        jumpDetail(data);
                                    }}>
                                    <Icon type="link" />
                                </a>
                            )
                        }
                    />
                    {String(nodeType) === '1' && (
                        <>
                            <Ellipsis title={riskTag || '- -'} />
                            <Ellipsis title={riskDecision || '- -'} style={{ color }} />
                        </>
                    )}
                </Row>
            </Row>
        </div>
    );
};
