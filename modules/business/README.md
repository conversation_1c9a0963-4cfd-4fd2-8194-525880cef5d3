> `business-module`的背景

`business-module` 作为其它应用的子仓库，将公共的场景（例如Layout，报告，指标等）统一在此应用中维护管理。解决npm带来的定制化问题。


## 注意事项

### 🚀 主版本开发
1. 基于master，拉release分支进行每个主版本的迭代，例如（release.154.sp01.2310）
2. 在使用到`business-module`的主应用中，找到`.gitmodules`文件，修改如下
```javascript
[submodule "src/modules"]
	path = src/modules
	url = *********************:app/business-module.git
    //同步修改这里的分支名
    //每个主版本发版后，记录当前主版本对应的business-module
    branch = release.154.sp01.2310 
```
> ⚠️ 为什么不写成master分支？担心不同版本在迭代过程中存在不向下兼容。如果是master，现场定制或者后续的版本中默认拉取master最新代码！

3. 走发版阶段，将 release.154.sp01.2310 分支和回 master，保证master分支为最新代码
   


### 🪜 定制化开发
如果有特殊场景需要做定制，该怎么做？
1. 找到定制化的base主版本，例如noah-react应用`.gitmodules`中的branch对应的版本
2. 基于上述1的分支，拉现场定制化分支，如`dev-1-5-4-2308-shbankpoc `
3. 在涉及到business-module改动的主应用中，找到`.gitmodules`文件，修改如下
```javascript
[submodule "src/modules"]
	path = src/modules
	url = *********************:app/business-module.git
    branch = dev-1-5-4-2308-shbankpoc 
```


## 本地开发
第一步：安装包，`peerDependencies`、`devDependencies`、`dependencies`。这里构建相关的包安装在`peerDependencies`，需要通过`install_peerDep.sh`脚本安装。
```
npm run install:all
```
第二步：启动项目。项目启动的webpack配置与我们平常的应用开发一样。只是目录位置放到了`build`目录下。
```
npm run start
```



## 目录结构预览
`build` 目录为开发模式所需，目的为了做效果的预览
![Alt text](image.png)
一般我们首次开发组件时，想要在开发模式下查看(非主应用中)，需要在test的router文件添加路由进行效果的预览

`Components` 目录为我们的共享核心代码

