import I18N, { getLang } from '~I18N';
import { useState } from 'react';
import { Button, Icon } from 'tntd';
import reportHTML from '../report_library/report.jst';
import service from '../service';

export default (props) => {
    const [exportLoad, setExportLoad] = useState(false);
    const { handleClick, allMap, title, ...otherProps } = props || {};
    // 下载前调用全局接口挂载在window上
    const onDownload = async () => {
        const { reportDetail, type } = handleClick();
        setExportLoad(true);
        const res = await service.reportDownload({
            token: reportDetail?.baseResultInfo.tokenId,
            id: reportDetail?.baseResultInfo.tokenId,
            type
        });
        window.reportData = res?.data || {};
        window.curLang = getLang();

        const { baseResultInfo } = window.reportData || {};
        const { baseFieldInfo = [], reportTime } = baseResultInfo || {};
        let name = document.querySelector('.report-head-title')?.innerText;
        if (baseFieldInfo?.[0]?.value) {
            name = `${baseFieldInfo?.[0]?.value}`;
        }
        name += `_${reportTime}`;
        if (window?.reportData?.baseResultInfo?.downloadReportTime) {
            window.reportData.baseResultInfo.reportTime = window.reportData.baseResultInfo.downloadReportTime;
        }
        const inlineData = `
            window.reportTitle='${title}'
            window.curLang = '${window.curLang}';
            window.downMode = ${true};
            window.reportData =  ${JSON.stringify(window.reportData)};
            window.allCompontlog = ${JSON.stringify(window.allCompontlog)};
            window.ruleAndIndexFieldMap = ${JSON.stringify(window.ruleAndIndexFieldMap)};
            window.dealTypeList = ${JSON.stringify(window.dealTypeList)};
            window.allMap = ${JSON.stringify(allMap)};
            document.title=${JSON.stringify(name)}
        `;
        const html = new Blob([reportHTML.replace('<script>{{inlineData}}</script>', `<script>${inlineData}</script>`)], {
            type: 'text/html'
        });
        const url = URL.createObjectURL(html);
        const eleLink = document.createElement('a');
        eleLink.download = `${name}.html`;
        eleLink.style.display = 'none';
        eleLink.href = url;
        // 触发点击
        document.body.appendChild(eleLink);
        eleLink.click();
        // 然后移除
        document.body.removeChild(eleLink);
        setExportLoad(false);
    };

    return (
        <Button loading={exportLoad} type="primary" onClick={onDownload} {...otherProps}>
            <Icon type="download" /> {I18N.exportbtn.index.xiaZai}
        </Button>
    );
};
