'use strict';

const path = require('path');
const webpack = require('webpack');
const WebpackDevServer = require('webpack-dev-server');
const configDev = require('./config');

const root = path.join(__dirname, '..');
const configPath = path.join(root, './build/webpack.dev.conf');

const config = require(configPath);

const entry = config.entry;

const devClient = ['webpack-dev-server/client?'];

// const devIp = '127.0.0.1'

Object.keys(entry).forEach((entryName) => {
    entry[entryName] = devClient.concat(entry[entryName]);
});

const compiler = webpack(config);

const server = new WebpackDevServer(
    {
        compress: true, // 一切服务都启用 gzip 压缩
        allowedHosts: 'all',
        host: configDev.dev.host,
        port: configDev.dev.port,
        static: [path.join(root, 'public')], // v5
        open: configDev.dev.autoOpenBrowser,
        onBeforeSetupMiddleware: (devServer) => {
            const { app } = devServer;

            function requireUncached(module) {
                try {
                    // 删除缓存，动态加载
                    delete require.cache[require.resolve(module)];
                    return require(module);
                } catch (e) {
                    console.log('没有加载到mock资源');
                }
            }

            // 根据 mock 请求发送响应 这里可以 是 function
            function sendValue(req, res, value) {
                if (typeof value === 'function') {
                    value = value(req, res);
                }
                res.send(value);
            }

            if (configDev.dev.mock) {
                console.log('执行onBeforeSetupMiddleware', configDev.dev.mock);

                // 处理 restful mock 接口
                const mockMap = require(path.join(root, 'mock'));

                // 对于每个 mock 请求，require mock 文件夹下的对应路径文件，并返回响应
                Object.keys(mockMap).forEach((mockPath) => {
                    app.all(mockPath, function (req, res) {
                        const value = requireUncached(path.join(root, 'mock', mockMap[mockPath]));
                        sendValue(req, res, value);
                    });
                });
            }
        },
        client: {
            webSocketURL: `ws://${configDev.dev.host}:${configDev.dev.port}/ws`
        },
        proxy: configDev.dev.proxyTable || {},
        historyApiFallback: {
            rewrites: [
                {
                    from: /!^\/api/g,
                    to: '/'
                }
            ]
        }
    },
    compiler
);

compiler.hooks.watchRun.tap('watchRun', () => {
    console.log('webpack building...');
});

// webpack config 编译完成
compiler.hooks.done.tap('done', (stats) => {
    const time = (stats.endTime - stats.startTime) / 1000;
    console.log(`webpack build success in ${time.toFixed(2)} s`);
});

server.listen(configDev.dev.port, null, () => {
    // console.log(`webpack-dev-server started at localhost:${configDev.dev.port}`);
});

server.startCallback(() => {
    // 将 html 的模板文件打包到 viewsPath 的目录下
    console.log(`webpack-dev-server started at localhost:${configDev.dev.port}`);
});
