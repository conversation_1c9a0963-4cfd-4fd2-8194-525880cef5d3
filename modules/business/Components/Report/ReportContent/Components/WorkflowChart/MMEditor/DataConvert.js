import I18N from '~I18N';
import { message, Modal } from 'tntd';
const convert = {
    format(data, editor, noMessage = false) {
        const res = {
            flowNodeDefinitions: [],
            flowLineDefinitions: []
        };
        const {
            graph: { node, line }
        } = editor;
        const { nodes = [], lines } = data;
        // if (!nodes.length) {
        //     message.error('策略不能为空'); //
        //     return;
        // }
        let errorMsgList = [];
        const nodesTypeMap = {};
        let noEndNum = 0;
        nodes.map((item, i) => {
            let { toLines, fromLines } = node.nodes[item.uuid];
            const nodeData = item.data || {};
            let data = {
                x: item.x,
                y: item.y,
                id: item.uuid,
                name: item.name,
                nodeType: item.type,
                attributes: {},
                incomingFields: [],
                outgoingFields: []
            };
            const checkInOut = (str) => {
                if (!fromLines.size && !toLines.size) {
                    errorMsgList.push(
                        <p key={`7-${i}`}>
                            [{str}
                            {I18N.mmeditor.dataconvert.queShaoShuRuShu}
                        </p>
                    );
                } else if (!fromLines.size) {
                    errorMsgList.push(
                        <p key={`7-${i}`}>
                            [{str}
                            {I18N.mmeditor.dataconvert.queShaoShuRuLiu}
                        </p>
                    );
                } else if (!toLines.size) {
                    errorMsgList.push(
                        <p key={`7-${i}`}>
                            [{str}
                            {I18N.mmeditor.dataconvert.queShaoShuChuLiu}
                        </p>
                    );
                } else {
                    return true;
                }
                return false;
            };
            switch (item.type) {
                case 'StartFlowNode':
                    if (toLines.size < 1) {
                        errorMsgList.push(<p key={`1-${i}`}>{I18N.mmeditor.dataconvert.kaiShiQueShaoShu}</p>);
                    }
                    if (nodesTypeMap['StartFlowNode']) {
                        errorMsgList.push(<p key={i}>{I18N.mmeditor.dataconvert.kaiShiKaiShiJie}</p>);
                    }
                    nodesTypeMap['StartFlowNode'] = true;
                    break;
                case 'EndFlowNode':
                    if (fromLines.size < 1) {
                        errorMsgList.push(<p key={`2-${i}`}>{I18N.mmeditor.dataconvert.jieShuQueShaoShu}</p>);
                    }

                    nodesTypeMap['EndFlowNode'] = true;
                    break;
                case 'ExclusiveGateway':
                    checkInOut(I18N.mmeditor.dataconvert.panDuan);
                    nodeData.type = nodeData.type !== 'end' ? 'start' : 'end';
                    const lines = nodeData.type !== 'end' ? toLines : fromLines;
                    if (lines.size < 2) {
                        errorMsgList.push(
                            <p key={`1-${i}`}>
                                [{item.name}
                                {I18N.mmeditor.dataconvert.zhiShaoPeiZhiTiao}
                                {nodeData.type === 'start' ? I18N.mmeditor.dataconvert.shuChu : I18N.mmeditor.dataconvert.shuRu}
                                {I18N.mmeditor.dataconvert.xian}
                            </p>
                        );
                    }
                    if (nodeData.type === 'start') {
                        let defaultNum = 0;
                        let flag = true;
                        lines.forEach((item) => {
                            const { processFlowCondition = {} } = line.lines[item].data?.data || {};
                            processFlowCondition.isDefault && defaultNum++;
                            if (!processFlowCondition.conditionName) {
                                flag = false;
                            }
                        });
                        if (!flag) {
                            errorMsgList.push(<p key={`excule${i}`}>{I18N.mmeditor.dataconvert.panDuanKaiShiTiao}</p>);
                        }
                        if (defaultNum !== 1) {
                            errorMsgList.push(
                                <p key={`2${i}`}>
                                    [{item.name}
                                    {I18N.mmeditor.dataconvert.xuYaoQieZhiNeng}
                                </p>
                            );
                        }
                    }
                    data.attributes.type = nodeData.type;
                    break;
                case 'ParallelGateway':
                    checkInOut(I18N.mmeditor.dataconvert.bingXing2);
                    nodeData.type = nodeData.type !== 'end' ? 'start' : 'end';
                    const paraLines = nodeData.type === 'end' ? fromLines : toLines;
                    if (paraLines.size < 2) {
                        errorMsgList.push(
                            <p key={`7-${i}`}>
                                {I18N.mmeditor.dataconvert.bingXing}
                                {nodeData.type === 'end' ? I18N.mmeditor.dataconvert.jieShu : I18N.mmeditor.dataconvert.kaiShi}
                                {I18N.mmeditor.dataconvert.zhiShaoPeiZhiTiao}
                                {nodeData.type === 'start' ? I18N.mmeditor.dataconvert.shuChu : I18N.mmeditor.dataconvert.shuRu}
                                {I18N.mmeditor.dataconvert.xian}
                            </p>
                        );
                    }
                    data.attributes.type = nodeData.type;
                    break;
                case 'RuleSetServiceNode':
                    checkInOut(I18N.mmeditor.dataconvert.guiZeJi);
                    if (!nodeData.eventId) {
                        errorMsgList.push(<p key={i}>{I18N.mmeditor.dataconvert.guiZeJiWeiShe}</p>);
                    }
                    data.attributes = {
                        ruleSetUuid: nodeData.eventId,
                        ruleSetName: nodeData.name
                    };
                    data.incomingFields = nodeData.inputParams?.map((item) => {
                        return {
                            fieldName: item.name,
                            fieldType: item.type
                        };
                    });
                    data.outgoingFields = nodeData.outputParams?.map((item) => {
                        return {
                            fieldName: item.fieldName,
                            fieldType: item.outputType,
                            mappingName: item.mappingName
                        };
                    });
                    break;
                case 'ThirdServiceNode':
                    checkInOut(I18N.mmeditor.dataconvert.sanFangFuWu);
                    if (!nodeData.selectService) {
                        errorMsgList.push(<p key={i}>{I18N.mmeditor.dataconvert.sanFangFuWuWei}</p>);
                    }
                    data.attributes = {
                        thirdServiceCode: nodeData.selectService,
                        thirdServiceName: nodeData.name
                    };
                    break;
                case 'ModelServiceNode':
                    checkInOut(I18N.mmeditor.dataconvert.moXing);

                    if (!nodeData.modelUuid) {
                        errorMsgList.push(<p key={i}>{I18N.mmeditor.dataconvert.moXingWeiSheZhi}</p>);
                    }
                    data.attributes = {
                        modelType: nodeData.modelType,
                        modelName: nodeData.modelName,
                        modelUuid: nodeData.modelUuid,
                        modelVersion: nodeData.modelVersion
                    };
                    data.incomingFields = nodeData.inputParams?.map((item) => {
                        return {
                            mappingName: item.mappingName,
                            fieldName: item.fieldName,
                            fieldType: item.fieldType,
                            dataType: item.dataType
                        };
                    });
                    data.outgoingFields = nodeData.outputParams?.map((item) => {
                        return {
                            fieldName: item.fieldName,
                            mappingName: item.mappingName,
                            dataType: item.dataType
                        };
                    });
                    break;
                case 'FunctionServiceNode':
                    checkInOut(I18N.mmeditor.dataconvert.hanShu);
                    if (!nodeData.code) {
                        errorMsgList.push(<p key={i}>{I18N.mmeditor.dataconvert.hanShuWeiSheZhi}</p>);
                    }
                    data.attributes = {
                        functionUuid: nodeData.code,
                        functionName: nodeData.name
                    };
                    break;
                case 'DecisionToolServiceNode':
                    checkInOut(I18N.mmeditor.dataconvert.jueCeGongJu);
                    if (!nodeData.type) {
                        errorMsgList.push(<p key={i}>{I18N.mmeditor.dataconvert.jueCeGongJuWei}</p>);
                    }
                    data.attributes = {
                        decisionType: nodeData.toolType,
                        decisionName: nodeData.name,
                        decisionUuid: nodeData.type,
                        version: nodeData.version
                    };
                    data.incomingFields = nodeData.inputParams?.map((item) => {
                        return {
                            fieldName: item.name,
                            dataType: item.dataType,
                            fieldType: item.fieldType
                        };
                    });
                    data.outgoingFields = nodeData.outputParams?.map((item) => {
                        return {
                            fieldName: item.fieldName,
                            dataType: item.dataType,
                            fieldType: item.fieldType,
                            mappingName: item.name
                        };
                    });
                    break;
                case 'ChampionServiceNode':
                    checkInOut(I18N.mmeditor.dataconvert.guanJunTiaoZhanZhe2);
                    data.attributes = {
                        type: nodeData.type || '1'
                    };
                    let defaultNum = 0;
                    let champion = 0;
                    let flag = true;
                    toLines.forEach((item) => {
                        const { processFlowCondition: championLine = {} } = line.lines[item].data?.data || {};
                        championLine.isDefault && defaultNum++;
                        championLine.type === 'champion' && champion++;
                        const isComplete =
                            nodeData.type === '2'
                                ? (championLine.isDefault && championLine.conditionName) || championLine.ruleConditionList
                                : championLine.ratio !== undefined;
                        if (!isComplete) {
                            flag = false;
                        }
                    });
                    if (!flag) {
                        errorMsgList.push(<p key={`excule${i}`}>{I18N.mmeditor.dataconvert.guanJunTiaoZhanZhe}</p>);
                    }
                    if (defaultNum !== 1 && parseInt(nodeData.type, 10) === 2) {
                        errorMsgList.push(
                            <p key={`2${i}`}>
                                [{item.name}
                                {I18N.mmeditor.dataconvert.xuYaoQieZhiNeng}
                            </p>
                        );
                    }
                    if (!champion) {
                        errorMsgList.push(
                            <p key={`2${i}`}>
                                [{item.name}
                                {I18N.mmeditor.dataconvert.xuYaoPeiZhiTiao}
                            </p>
                        );
                    }
                    if (toLines.size < 2) {
                        errorMsgList.push(
                            <p key={`2${i}`}>
                                [{item.name}
                                {I18N.mmeditor.dataconvert.xuYaoPeiZhiZhi}
                            </p>
                        );
                    }
                    break;
                case 'ActionServiceNode':
                    checkInOut(I18N.mmeditor.dataconvert.dongZuo);
                    const { customDialogData = {} } = nodeData || {};
                    data.attributes = {
                        actionType: customDialogData.actionType || 1,
                        name: customDialogData.name,
                        finalDecision: customDialogData.finalDecision
                    };
                    break;
                case 'SuspendFlowNode':
                    checkInOut(I18N.mmeditor.dataconvert.jiXuBuChong);
                    const { decisionMakingDialog = {} } = nodeData || {};
                    data.attributes = {
                        serviceCode: decisionMakingDialog.selectedService
                    };
                    break;
                case 'SubDecisionFlowNode':
                    checkInOut(I18N.mmeditor.dataconvert.liuChengMuBan);
                    data.flowLineDefinitions = nodeData.flowLineDefinitions;
                    data.flowNodeDefinitions = nodeData.flowNodeDefinitions;
                    data.attributes = {};
                    break;
                default:
            }
            if (toLines.length < 1) {
                if (item.type !== 'EndFlowNode') {
                    noEndNum++;
                }
            }
            res.flowNodeDefinitions.push(data);
        });

        res.flowLineDefinitions = lines.map((item, i) => {
            let data = {
                id: item.uuid,
                fromPoint: item.fromPoint,
                toPoint: item.toPoint,
                attributes: {},
                sourceNodeId: item.from,
                targetNodeId: item.to
            };
            const lineData = item?.data || {};
            const fromNode = node.nodes[item.from].data;
            switch (fromNode.type) {
                case 'ExclusiveGateway':
                    if (fromNode.data.type === 'start') {
                        const { processFlowCondition = {} } = lineData;
                        data.lineType = 'ExclusiveConditionLine';
                        data.attributes.isDefault = processFlowCondition.isDefault;
                        if (!data.attributes.isDefault) {
                            data.attributes.priority = processFlowCondition.priority;
                            data.attributes.fieldList = processFlowCondition.fieldList || [];
                            data.attributes.condition = processFlowCondition.ruleConditionList
                                ? JSON.stringify(processFlowCondition.ruleConditionList)
                                : null;
                        }
                        data.name = processFlowCondition.conditionName;
                    }
                    break;
                case 'ChampionServiceNode':
                    data.lineType = 'ChampionConditionLine';
                    const { processFlowCondition: championLine = {} } = lineData;
                    data.name = championLine.conditionName;
                    data.attributes.type = championLine.type;
                    if (fromNode.data.type === '2') {
                        data.attributes.isDefault = championLine.isDefault;
                        if (!data.attributes.isDefault) {
                            data.attributes.ratio = championLine.ratio;
                            data.attributes.priority = championLine.priority;
                            data.attributes.fieldList = championLine.fieldList || [];
                            data.attributes.condition = championLine.ruleConditionList
                                ? JSON.stringify(championLine.ruleConditionList)
                                : null;
                        }
                    } else {
                        data.attributes.ratio = championLine.ratio;
                        data.attributes.priority = championLine.priority;
                    }
                    break;
                default:
                    break;
            }
            return data;
        });

        if (!noMessage && errorMsgList.length > 0) {
            const errorMsgListMap = {};
            errorMsgList = errorMsgList.filter((item) => {
                if (errorMsgListMap[item.props.children]) {
                    return false;
                }
                errorMsgListMap[item.props.children] = true;
                return true;
            });
            Modal.warning({
                zIndex: 1100,
                title: I18N.mmeditor.dataconvert.ceLuePeiZhiBu2, //
                content: <div>{errorMsgList}</div>
            });
            return false;
        }
        if (!noMessage && res.flowNodeDefinitions.length === 0) {
            message.warn(I18N.mmeditor.dataconvert.ceLuePeiZhiBu);
            return false;
        }
        this.res = res;
        return res;
    },

    convert(data, editor) {
        const res = {
            nodes: [],
            lines: []
        };
        const nodesMap = {};
        const { flowNodeDefinitions = [], flowLineDefinitions = [] } = data;
        res.nodes = flowNodeDefinitions.map((item) => {
            nodesMap[item.id] = item;
            item.attributes = item.attributes || {};
            item.incomingFields = item.incomingFields || [];
            item.outgoingFields = item.outgoingFields || [];
            let node = {
                uuid: item.id,
                x: item.x,
                y: item.y,
                type: item.nodeType,
                name: item.name,
                data: {}
            };
            switch (item.nodeType) {
                case 'StartFlowNode':
                    node.name = I18N.mmeditor.dataconvert.kaiShi;
                    break;
                case 'EndFlowNode':
                    node.name = I18N.mmeditor.dataconvert.jieShu;
                    break;
                case 'ExclusiveGateway':
                    node.data.type = item.attributes.type;
                    if (node.data.type === 'start') {
                        node.name = I18N.mmeditor.dataconvert.panDuanKaiShi;
                    } else {
                        node.name = I18N.mmeditor.dataconvert.panDuanJieShu;
                    }
                    break;
                case 'ParallelGateway':
                    node.data.type = item.attributes.type;
                    node.name = I18N.mmeditor.dataconvert.bingXingKaiShi;
                    if (node.data.type === 'start') {
                        node.name = I18N.mmeditor.dataconvert.bingXingKaiShi;
                    } else {
                        node.name = I18N.mmeditor.dataconvert.bingXingJieShu;
                    }
                    break;
                case 'RuleSetServiceNode':
                    node.data = {
                        eventId: item.attributes.ruleSetUuid,
                        name: item.attributes.ruleSetName,
                        code: item.attributes.code,
                        inputParams: item.incomingFields.map((each) => {
                            return {
                                name: each.fieldName,
                                type: each.fieldType
                            };
                        }),
                        outputParams: item.outgoingFields.map((each) => {
                            return {
                                fieldName: each.fieldName,
                                outputType: each.fieldType,
                                mappingName: each.mappingName
                            };
                        })
                    };
                    break;
                case 'ThirdServiceNode':
                    node.data = {
                        selectService: item.attributes.thirdServiceCode,
                        name: item.attributes.thirdServiceName
                    };
                    break;
                case 'ModelServiceNode':
                    node.data = {
                        modelType: item.attributes.modelType,
                        modelName: item.attributes.modelName,
                        modelUuid: item.attributes.modelUuid,
                        modelVersion: item.attributes.modelVersion,
                        inputParams: item.incomingFields,
                        outputParams: item.outgoingFields
                    };
                    break;
                case 'FeatureServiceNode':
                    node.data = {
                        selectService: item.attributes.thirdServiceCode,
                        name: item.attributes.thirdServiceName
                    };
                    break;
                case 'FunctionServiceNode':
                    node.data = {
                        code: item.attributes.functionUuid,
                        name: item.attributes.functionName
                    };
                    break;
                case 'DecisionToolServiceNode':
                    node.data = {
                        toolType: item.attributes.decisionType,
                        name: item.attributes.decisionName,
                        type: item.attributes.decisionUuid,
                        version: item.attributes.version,
                        inputParams: item.incomingFields.map((each) => {
                            return {
                                dataType: each.dataType,
                                name: each.fieldName,
                                fieldType: each.fieldType
                            };
                        }),
                        outputParams: item.outgoingFields.map((each) => {
                            return {
                                fieldName: each.fieldName,
                                name: each.mappingName,
                                dataType: item.dataType,
                                fieldType: item.fieldType
                            };
                        })
                    };
                    break;
                case 'ChampionServiceNode':
                    node.data = {
                        type: item.attributes.type || '1'
                    };
                    break;
                case 'ActionServiceNode':
                    node.data = {
                        customDialogData: {
                            name: item.attributes.actionType === 1 ? undefined : item.name,
                            actionType: item.attributes.actionType,
                            finalDecision: item.attributes.finalDecision
                        }
                    };
                    break;
                case 'SuspendFlowNode':
                    node.name = item.name || I18N.mmeditor.dataconvert.jiXuBuChong;
                    node.data = {
                        decisionMakingDialog: {
                            selectedService: item.attributes.serviceCode
                        }
                    };
                    break;
                case 'SubDecisionFlowNode':
                    node.name = item.name || I18N.mmeditor.dataconvert.jiXuBuChong;
                    node.data = {
                        ...item
                    };
                    break;
                case 'ScoreCardServiceNode':
                    node.data = {
                        scoreCardName: item.attributes.scoreCardName,
                        scoreCardCode: item.attributes.scoreCardCode,
                        scoreCardUuid: item.attributes.scoreCardUuid,
                        inputParams: item.incomingFields.map((each) => {
                            return {
                                ...each,
                                name: each.fieldName
                            };
                        }),
                        outputParams: item.outgoingFields
                    };
                    break;
                case 'ChildFlowNode':
                    node.data = {
                        policyName: item.name,
                        policyUuid: item.attributes.policyUuid,
                        policyCode: item.attributes.policyCode
                    };
                    if (item.attributes.round) {
                        node.data.mode = item.attributes.mode === 'serial' ? 'serial' : 'parallel';
                    }
                    break;

                default:
            }
            return node;
        });
        res.lines = flowLineDefinitions.map((item) => {
            item.attributes = item.attributes || {};
            const fromNode = nodesMap[item.sourceNodeId];
            let line = {
                uuid: item.id,
                fromPoint: item.fromPoint,
                toPoint: item.toPoint,
                data: {},
                from: item.sourceNodeId,
                to: item.targetNodeId
            };
            switch (item.lineType) {
                case 'ExclusiveConditionLine':
                    line.data = {
                        processFlowCondition: {
                            isDefault: item.attributes.isDefault,
                            priority: item.attributes.priority,
                            ruleConditionList: item.attributes.condition ? JSON.parse(item.attributes.condition || '[]') : undefined,
                            conditionName: item.name
                        }
                    };
                    line.label = item.name;
                    break;
                case 'ChampionConditionLine':
                    line.data = {
                        processFlowCondition: {
                            type: item.attributes.type,
                            ratio: item.attributes.ratio,
                            isDefault: item.attributes.isDefault,
                            priority: item.attributes.priority,
                            fieldList: item.attributes.fieldList,
                            conditionName: item.name,
                            ruleConditionList: item.attributes.condition ? JSON.parse(item.attributes.condition) : undefined
                        }
                    };
                    const isComplete =
                        fromNode.attributes.type === '2'
                            ? (item.attributes.isDefault && item.name) || item.attributes.condition
                            : item.attributes.ratio !== undefined;
                    line.label = item.name;
                    line.className = isComplete ? '' : 'red-line';
                    break;
                default:
                    break;
            }
            return line;
        });
        return res;
    }
};
export default convert;
