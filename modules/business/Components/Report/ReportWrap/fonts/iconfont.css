@font-face {
  font-family: "report-iconfont"; /* Project id  */
  src: url('iconfont.ttf?t=1676538573406') format('truetype');
}

.report-iconfont {
  font-family: "report-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-body:before {
  content: "\e7ed";
}

.icon-detailed:before {
  content: "\e7ee";
}

.icon-to-top:before {
  content: "\e7f1";
}

.icon-simple:before {
  content: "\e7f3";
}

.icon-relation:before {
  content: "\e7f4";
}

