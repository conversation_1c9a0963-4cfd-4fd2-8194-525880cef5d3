import { Ellipsis } from 'tntd';

export default (props) => {
	const { className, label, value, rate, style } = props;
	let labelStyle = {};
	let valueStyle = {};
	if (rate) {
		labelStyle = { width: rate.labelWidth };
		valueStyle = { width: rate.valueWidth };
	}
	return (
		<div className={`item ${className || ''}`} style={style}>
			<div className="label" style={labelStyle}>
				<Ellipsis title={label} widthLimit={100} />
			</div>
			<div className="value" style={valueStyle}>
				<Ellipsis title={value} style={{ width: '100%' }} />
			</div>
		</div>
	);
};
