body {
    height: 100%;
    overflow-y: hidden;
    background-color: #f0f2f5;
    text-rendering: optimizeLegibility;
    text-rendering: initial;
    -webkit-font-smoothing: initial;
    font-family: "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI",
        "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue",
        Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol";
}

.test-layout-wrap{
    min-height: 100vh;
    .left-nav{
        width: 240px;
        >.ant-menu-inline{
            padding: 8px 0;
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .ant-menu-item,.ant-menu-submenu-title{
            height: 36px;
            line-height: 36px;
        }
    }
    .content-wrap{
        flex:1;
        width:calc(100% - 240px);
        padding: 16px;
    }
}

.globalSpin {
    width: 100%;
    margin: 40px 0 !important;
    text-align: center;
    display: inherit;
}

.mock-login{
    position: absolute;
    right: 12px;
    top:4px;
}
