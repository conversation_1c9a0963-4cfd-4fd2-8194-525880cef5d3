@import "./fonts/iconfont.css";
.report-content-wrap{
    height: 100%;
    overflow-y: auto;
    padding: 16px;
}
.tnt-current-v3{
    .report-content-wrap{
        padding: 0;
    }
}
.affix-nav{
    .affix-nav-item{
        position: relative;
        width: 44px;
        height: 44px;
        background: #FFFFFF;
        box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.19);
        border-radius: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        user-select: none;
        &:not(:last-of-type){
            margin-bottom: 8px;
        }
        &:hover{
            color: #126BFB;
        }
    }
    .ant-menu-root{
        border:none !important;
        width: 44px;
        height: 44px !important;
        border-radius: 100%;
        text-align: center;
        .ant-menu-submenu-arrow{
            display: none !important;
        }
        .ant-menu-submenu-title{
            padding: 0 !important;
            margin: 0 !important;
            width: 44px;
            height: 44px !important;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}


.report-menu-group{
    width: 160px;
    .ant-menu{
        padding:8px 0 !important;
        li.alarm-report-menu-item, li.ant-menu-submenu>.ant-menu-submenu-title {
            margin: 0 !important;
            padding: 0 12px !important;
            height: 36px !important;
            line-height: 36px !important;
            .ant-menu-submenu-arrow{
                right: 6px !important;
            }
        }
    }
}
