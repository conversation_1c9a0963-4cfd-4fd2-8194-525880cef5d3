import { useState, useEffect, useRef } from 'react';
import { Tabs } from 'tntd';
import BaseInfo from './BaseInfo';
import CommonInfo from './CommonInfo';
import IndexInfo from './IndexInfo';
import ChildFlow from './ChildFlow';
import './index.less';
import ScoreCard from './ScoreCard';
import ProcessInfo from './ProcessInfo';
const { TabPane } = Tabs;

export default (props) => {
    const { defaultToken, data, nodeTypeMap = {}, nodeDefault = [], onTabChange, canJump } = props || {};
    const { baseResultInfo } = data || {};
    const { nodeType = [], tokenId } = baseResultInfo || {};
    const curToken = useRef();

    const [nodesList, setNodesList] = useState([]);
    const [curTab, setCurTab] = useState(0);

    const [versionToken, setVersionToken] = useState();

    useEffect(() => {
        if (baseResultInfo) {
            if (defaultToken && curToken.current && defaultToken !== curToken.current) {
                setVersionToken(defaultToken);
            }
            curToken.current = defaultToken;

            const nodes = nodeDefault
                ?.concat(nodeType)
                ?.filter((v) => !['StartFlowNode', 'EndFlowNode', 'ParallelGateway', 'ExclusiveGateway'].includes(v))
                ?.map((curNode) => nodeTypeMap[curNode]);
            setNodesList(nodes);
            setCurTab(0);
        }
    }, [baseResultInfo]);

    const handleTabChange = (key) => {
        onTabChange && onTabChange(key);
        setCurTab(Number(key));
    };

    return (
        <div className="report-detail-wrap">
            <Tabs activeKey={String(curTab)} onChange={handleTabChange} className="report-tabs">
                {nodesList?.map((v) => {
                    return v?.label && <TabPane tab={v?.label} key={v?.key} />;
                })}
            </Tabs>
            <div className="report-detail-tab-content">
                {curTab === 0 && <BaseInfo {...props} data={data} canJump={canJump} />}
                {![0, 6, 8, 10].includes(curTab) && <CommonInfo nodeType={curTab} tokenId={tokenId} canJump={canJump} {...props} />}
                {[6].includes(curTab) && <IndexInfo nodeType={curTab} tokenId={tokenId} {...props} />}
                {[8].includes(curTab) && (
                    <ChildFlow versionToken={versionToken} nodeType={curTab} tokenId={tokenId} defaultToken={defaultToken} {...props} />
                )}
                {[9].includes(curTab) && <ScoreCard {...props} />}
                {[10].includes(curTab) && <ProcessInfo {...props} />}
            </div>
        </div>
    );
};
