@keyframes dashing {
	from {
		stroke-dashoffset: 200;
	}
	to {
		stroke-dashoffset: 0;
	}
}
.mm-editor {
	width: 100%;
	height: 100%;
	position: relative;
	.mm-editor-back{
		width:100%;
		height:100%;
		position: absolute;
		z-index: 0;
		left:0;
		top:0;
	}
	.mm-minimap{
		position: absolute;
		top:10px;
		right:10px;
		background:#fff;
		border:1px solid #cecece;
		border-radius:5px;
		overflow: hidden;
        width: 160px;
        height: 127px;
		.drag-rect{
			position: absolute;
			left:0px;
			box-sizing: content-box;
			top:0px;
			border:2px solid #08c;
			cursor: pointer;
            transition: all 200ms ease;
		}
		.drag-point{
			width:10px;
			height: 10px;
			border-radius: 5px;
			border:2px solid #08c;
			background: #fff;
			position: absolute;
			right:-5px;
			bottom:-5px;
			cursor: nwse-resize;
		}
	}
	> .mm-editor-svg {
		cursor: grab;
		width: 100%;
		height: 100%;
		position: absolute;
		svg {
			position: absolute;
			left: 0;
			top: 0;
			outline: none;
		}
		* {
			transition: x, y, transform, cx, cy, width, stroke, height, fill 400ms;
			outline: none;
		}
		.mm-node {
			cursor: move;

			.mm-node-shape {
				&:hover {
					> .icon-node {
						stroke: #4c79ff;
						// fill-opacity: 0.2!important;
					}
				}
				&.active {
					> .icon-node {
						transition: stroke 400ms;
						stroke: rgba(76, 121, 255, 1);
					}
				}
				&.success {
					.icon-node {
						stroke: green;
						fill: #fff;
					}
				}
				&.error {
					.icon-node {
						stroke: red;
					}
				}
				&.running {
					.icon-node {
						stroke: #4c79ff;
					}
				}
			}
		}
		.mm-line {
			&.active {
				.mm-line-shape {
					stroke-width: 5px;
					opacity: 0.5;
				}
				.mm-line-arrow {
					opacity: 0.5;
				}
			}
			&:hover {
				.mm-line-shape {
					stroke-width: 5px;
					opacity: 0.5;
				}
				.mm-line-arrow {
					opacity: 0.5;
				}
			}
			&.running {
				.mm-line-shape path{
					stroke-dasharray: 5 !important;
					animation: dashing 5s linear infinite;
				}
			}
			.mm-line-shape {
				cursor: pointer;
			}
			.mm-line-arrow {
				cursor: crosshair;
			}
			.mm-line-label{
				text{
					text-anchor: middle;
				}
			}
		}
		.anchor-line{
			stroke: #4c79ff;
		}
		.link-points-g {
			.mm-link-points {
				&:hover,&.hover {
					fill: #4c79ff;
					cursor: crosshair;
				}
			}
		}
	}
	> .mm-editor-html {
		width: 100%;
		height: 100%;
		position: absolute;
	}
	.anchor-back{
		position: absolute;
		top:0;
		left:0;
	}
}
.mm-node-wrapper{
	border:1px solid #08c;
	border-radius: 8px;
	box-sizing: border-box;
	display: flex;
	justify-content: center;
	background-color: #fff;
	align-items: center;
}