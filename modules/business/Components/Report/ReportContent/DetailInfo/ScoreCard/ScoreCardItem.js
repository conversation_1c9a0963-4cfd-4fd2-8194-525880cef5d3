import I18N from '~I18N';
import React, { useMemo, memo } from 'react';
import { Ellipsis, Table, Tag, Popover, Descriptions } from 'tntd';
import { RuleTree } from '@tddc/td-tree-view';
import InfoSection, { InfoTitle, InfoTable, InfoTd } from '../../Components/InfoSection';
import { isJSON } from '@tntd/utils';
import { translationStr, countNestedChildren } from '../../util';
import './index.less';
const mapChineseIndex = [
    I18N.scorecard.scorecarditem.yi,
    I18N.scorecard.scorecarditem.er,
    I18N.mmshapes.initshapes.san,
    I18N.scorecard.scorecarditem.si,
    I18N.scorecard.scorecarditem.wu,
    I18N.scorecard.scorecarditem.liu,
    I18N.scorecard.scorecarditem.qi,
    I18N.scorecard.scorecarditem.ba,
    I18N.scorecard.scorecarditem.jiu
];

const ScoreCardItem = ({ detail, allMap }) => {
    const { ruleAndIndexFieldList, ruleAndIndexFieldMap } = allMap;
    /** 评分卡详情 */
    const data = useMemo(() => {
        return Array.isArray(detail?.scorecardExcuterDetail) ? detail?.scorecardExcuterDetail : [];
    }, [detail]);

    /** 大类层级 */
    const categoriesNum = useMemo(() => {
        return countNestedChildren(data) - 1;
    }, [data]);

    const dataSource = useMemo(() => {
        const flattenRecursive = (data, attrs = {}, list = [], level = 1) => {
            if (Array.isArray(data)) {
                for (const item of data) {
                    const { children, ...rest } = item;

                    if (children?.some((item) => item?.hasOwnProperty('inputParams'))) {
                        list.push({ ...attrs, variables: { ...rest, children } });
                    } else if (children && children.length > 0 && !item.hasOwnProperty('scoreMethod')) {
                        let key = `category_${level}`;

                        flattenRecursive(children, { ...attrs, [key]: { ...rest } }, list, level + 1);
                    } else {
                        /** 最后一层为入参 */
                        list.push({ ...attrs, variables: { ...rest, children } });
                    }
                }
            }

            return list;
        };
        return flattenRecursive(data);
    }, [categoriesNum]);

    const columns = useMemo(() => {
        let defaultColumn = [
            {
                title: I18N.scorecard.scorecarditem.bianLiang,
                width: '18%',
                dataIndex: 'variables',
                render: (text) => {
                    let { id, type, ruleCondition, score, weight, scoreMethod, name } = text || {};
                    ruleCondition = isJSON(ruleCondition) ? JSON.parse(ruleCondition) : {};

                    if (type === 'rule') {
                        return (
                            <>
                                <Popover
                                    overlayClassName="report-score-rule-tree"
                                    overlayStyle={{ width: 850, maxHeight: 400, overflow: 'scroll' }}
                                    placement="top"
                                    title={''}
                                    content={
                                        <RuleTree
                                            allMap={allMap}
                                            ruleTemplateList={[]}
                                            dataSourceList={{
                                                ruleAndIndexFieldList
                                            }}
                                            value={
                                                ruleCondition?.customLogicOperator
                                                    ? ruleCondition?.customLogicOperator
                                                    : translationStr(ruleCondition?.children, ruleCondition?.logicOperator)
                                            }
                                            showLogic={true}
                                            rules={ruleCondition?.children || []}
                                            logicOperator={ruleCondition?.logicOperator}
                                        />
                                    }
                                    trigger="click">
                                    <a>{name}</a>
                                </Popover>
                                <div style={{ color: 'rgb(158, 169, 190)' }}>
                                    {I18N.scorecard.scorecarditem.deFen}
                                    {score}{' '}
                                    {weight && Number(scoreMethod) !== 2 && Number(detail?.scoreCardMode) === 2
                                        ? I18N.template(I18N.scorecard.scorecarditem.quanZhongWEI, { val1: weight })
                                        : ''}
                                    )
                                </div>
                            </>
                        );
                    }
                    return (
                        <Ellipsis
                            title={
                                <>
                                    <div>{ruleAndIndexFieldMap?.[id]?.dName || '--'}</div>
                                    <span style={{ color: 'rgb(158, 169, 190)' }}>
                                        {I18N.scorecard.scorecarditem.deFen}
                                        {score}{' '}
                                        {weight && Number(scoreMethod) !== 2 && Number(detail?.scoreCardMode) === 2
                                            ? I18N.template(I18N.scorecard.scorecarditem.quanZhongWEI, { val1: weight })
                                            : ''}
                                        )
                                    </span>
                                </>
                            }
                            widthLimit="100"
                        />
                    );
                }
            },
            {
                title: I18N.ruleinfo.index.ruCan,
                dataIndex: 'joinIn',
                render: (text, row) => {
                    const { variables } = row;
                    const { inputParams } = variables?.['children']?.[0] || {};
                    if (Array.isArray(inputParams)) {
                        return (
                            <>
                                {inputParams.map((i, index) => {
                                    const bool = i.hasOwnProperty('dName');
                                    return (
                                        <div key={index} style={{ display: 'flex' }}>
                                            <Ellipsis
                                                title={bool ? i.dName : ruleAndIndexFieldMap?.[i?.name]?.dName || '--'}
                                                widthLimit="100px"
                                                style={{ width: 'auto' }}
                                            />
                                            :
                                            <Ellipsis
                                                title={!['', undefined, null].includes(i?.value) ? String(i?.value) : ''}
                                                widthLimit="150px"
                                            />
                                        </div>
                                    );
                                })}
                            </>
                        );
                    }

                    return '';
                }
            },
            {
                title: I18N.scorecard.scorecarditem.deFen2,
                width: 100,
                dataIndex: 'score',
                render: (text, row) => {
                    const { variables } = row;
                    const { score } = variables || {};
                    return <Ellipsis title={score} widthLimit="100" />;
                }
            }
        ];

        if (!isNaN(categoriesNum) && categoriesNum >= 1) {
            const list = [];
            const categoeryArray = Array.from({ length: categoriesNum }, (_, index) => index + 1);
            categoeryArray.forEach((i, index) => {
                list.push({
                    title: I18N.template(I18N.scorecard.scorecarditem.mAPCH, { val1: mapChineseIndex[index] }),
                    dataIndex: `category_${i}`,
                    width: '18%',
                    render: (text, row, _index) => {
                        let rowSpan = 0;
                        const arr = Array.from({ length: i }, (_, index) => index + 1);

                        const index = dataSource.findIndex((el) => {
                            return arr.every((num) => {
                                return el[`category_${num}`]?.id === row[`category_${num}`]?.id;
                            });
                        });

                        const len = dataSource.filter((el) => {
                            return arr.every((num) => {
                                return el[`category_${num}`]?.id === row[`category_${num}`]?.id;
                            });
                        })?.length;

                        rowSpan = index === _index ? len : 0;
                        const obj = {
                            children: (
                                <>
                                    <Ellipsis title={text?.name} widthLimit="100%" />
                                    <span style={{ color: 'rgb(158, 169, 190)' }}>
                                        {I18N.scorecard.scorecarditem.deFen}
                                        {text?.score}{' '}
                                        {text?.weight && Number(detail?.scoreCardMode) === 2
                                            ? I18N.template(I18N.scorecard.scorecarditem.quanZhongTEX, { val1: text?.weight })
                                            : ''}
                                        ){' '}
                                    </span>
                                </>
                            ),
                            props: { rowSpan }
                        };
                        return obj;
                    }
                });
            });
            defaultColumn = [...list, ...defaultColumn];
        }
        return defaultColumn;
    }, [categoriesNum, dataSource]);

    return (
        <div className="report-one-part-item">
            <Descriptions title={I18N.scorecard.scorecarditem.pingFenKa} />
            <InfoSection>
                <InfoTable cols={3}>
                    <InfoTd
                        label={I18N.report.constant.pingFenKaMingCheng}
                        value={
                            <>
                                {detail?.extension?.version ? (
                                    <Tag
                                        color="rgba(148, 95, 185, 0.1)"
                                        style={{ border: '1px solid #945FB9', color: '#945FB9', verticalAlign: 'middle' }}>
                                        V{detail?.extension?.version}
                                    </Tag>
                                ) : (
                                    ''
                                )}
                                <span>{detail?.displayName}</span>
                            </>
                        }
                    />
                    <InfoTd label={I18N.scorecard.scorecarditem.pingFenKaZongFen2} value={detail?.totalScore} />
                    <InfoTd label={I18N.scorecard.scorecarditem.pingFenKaZongFen} value={detail?.singleScore} />
                </InfoTable>
            </InfoSection>
            <Table
                className="mt20"
                bordered
                columns={columns}
                dataSource={dataSource}
                pagination={false}
                rowKey={(row, index) => index}
                scroll={{ x: true }}
            />
        </div>
    );
};

export default memo(ScoreCardItem);
