import I18N from '~I18N';
import { useState, useEffect } from 'react';
import { Icon as TntdIcon, Tag, TntdModal, Button, Descriptions, Tooltip, Icon, Ellipsis } from 'tntd';
import { cloneDeep } from 'lodash';
import history from '../../../../../website/src/utils/history.js';
import WorkflowChart from '../../Components/WorkflowChart';
import H1Title from '../../Components/H1Title';
import { InfoDesc } from '../../Components/InfoSection';
import { getFieldType } from '../../util';

import './index.less';

export default (props) => {
    const { data = {}, canJump, hideReqDesc } = props;
    const { baseResultInfo, applyInfoGroupList = [], resGroupList = [], policyVersionDTO = {} } = data || {};
    const { tokenId } = baseResultInfo || {};
    const [subflowData, setSubflowData] = useState({});
    //流程模板打开课展示数据
    const [newflowLogData, setNewFlowLogData] = useState({});

    // 是否弹窗展示子策略以及全屏
    const [fullscreen, setFullscreen] = useState(false);
    const [visible, setVisible] = useState(false);

    useEffect(() => {
        if (tokenId) {
            initSubflowData();
        }
    }, [tokenId]);

    // 初始化子流程
    const initSubflowData = () => {
        setSubflowData({
            graphJson: [],
            visible: false
        });
    };

    useEffect(() => {
        return () => {
            initSubflowData();
            setVisible(false);
            setFullscreen(false);
        };
    }, []);

    useEffect(() => {
        if (subflowData?.visible) {
            setVisible(true);
            setFullscreen(true);
        }
    }, [subflowData]);

    return (
        <>
            {!!applyInfoGroupList?.length && (
                <div className="report-section">
                    <H1Title
                        title={
                            <>
                                {I18N.baseinfo.index.ruCanXinXi}
                                {!hideReqDesc && (
                                    <Tooltip title={I18N.baseinfo.index.qianZhiFuWuZhong}>
                                        <Icon type="info-circle" className="ml4" />
                                    </Tooltip>
                                )}
                            </>
                        }
                    />
                    {applyInfoGroupList?.map((item, i) => {
                        return (
                            <>
                                <Descriptions
                                    key={i}
                                    className="tntd-description-divider description-item-wrap"
                                    title={item.groupDisplayName || '- -'}>
                                    {item.fieldInfoList.map((item1, index) => {
                                        const { displayName } = item1 || {};
                                        const { typeName, color } = getFieldType(item1);
                                        const labelShow = (
                                            <>
                                                <span style={{ color }}>{typeName}</span>
                                                {displayName}
                                            </>
                                        );
                                        return (
                                            <Descriptions.Item colon={false} key={index}>
                                                <InfoDesc
                                                    data={{
                                                        ...(item1 || {}),
                                                        label: labelShow
                                                    }}
                                                />
                                            </Descriptions.Item>
                                        );
                                    })}
                                </Descriptions>
                            </>
                        );
                    })}
                </div>
            )}

            {!!resGroupList?.length && (
                <div className="report-section">
                    <H1Title
                        title={
                            <>
                                {I18N.baseinfo.index.chuCanXinXi}
                                <Tooltip title={I18N.baseinfo.index.ceLueSuoYouJie}>
                                    <Icon type="info-circle" className="ml4" />
                                </Tooltip>
                            </>
                        }
                    />

                    {resGroupList?.map((item, i) => {
                        return (
                            <>
                                <Descriptions
                                    key={i}
                                    className="tntd-description-divider description-item-wrap"
                                    title={item.groupDisplayName || '- -'}>
                                    {item.fieldInfoList.map((item1, index) => {
                                        return (
                                            <Descriptions.Item colon={false} key={index}>
                                                <InfoDesc
                                                    data={{
                                                        ...(item1 || {}),
                                                        label: item1?.displayName
                                                    }}
                                                />
                                            </Descriptions.Item>
                                        );
                                    })}
                                </Descriptions>
                            </>
                        );
                    })}
                </div>
            )}

            {policyVersionDTO?.graphJson && (
                <div className="report-section">
                    <div className="report-policy-name-wrap">
                        <Ellipsis
                            // className="report-policy-name"
                            prefix={'工作流' + '：'}
                            title={<>{policyVersionDTO?.displayName}</>}
                            suffix={
                                <>
                                    {canJump && (
                                        <a
                                            style={{ marginLeft: '4px' }}
                                            onClick={() => {
                                                history.push(
                                                    `/handle/workflow/arrange?currentTab=2&displayName=${policyVersionDTO?.displayName}`
                                                );
                                            }}>
                                            <Icon type="link" />
                                        </a>
                                    )}
                                    <Tag className="first-tag">V{policyVersionDTO?.version}</Tag>
                                </>
                            }
                        />
                    </div>

                    <div className="report-section-workflow-chart">
                        <WorkflowChart
                            {...props}
                            canJump={canJump}
                            setSubflowData={setSubflowData}
                            graphJson={policyVersionDTO?.graphJson}
                            tokenId={tokenId}
                            setNewFlowLogData={setNewFlowLogData}
                            opera={[
                                <Button
                                    key="fullscreen"
                                    onClick={() => {
                                        setVisible(true);
                                        setFullscreen(true);
                                    }}>
                                    <TntdIcon type="fullscreen" className="fullscreen-Icon" />
                                </Button>
                            ]}
                        />
                    </div>

                    <TntdModal
                        title={
                            subflowData?.visible ? (
                                <>
                                    {'流程模板'}
                                    <span style={{ marginLeft: '4px' }}>{subflowData?.graphJson.name}</span>
                                </>
                            ) : (
                                <>
                                    {I18N.baseinfo.index.ceLueMingCheng}
                                    <span style={{ marginLeft: '4px' }}>
                                        {policyVersionDTO?.policyName}
                                        <Tag style={{ marginLeft: '6px' }} className="first-tag">
                                            V{policyVersionDTO?.version}
                                        </Tag>
                                    </span>
                                </>
                            )
                        }
                        className="full-modal"
                        visible={visible}
                        fullscreen={fullscreen}
                        onCancel={() => {
                            setVisible(false);
                            setFullscreen(false);
                            initSubflowData();
                        }}>
                        {!!visible && (
                            <WorkflowChart
                                {...props}
                                getFlowLog={false}
                                setSubflowData={setSubflowData}
                                graphJson={subflowData?.visible ? subflowData?.graphJson : policyVersionDTO?.graphJson}
                                tokenId={tokenId}
                                flowLogData={newflowLogData}
                            />
                        )}
                    </TntdModal>
                </div>
            )}
        </>
    );
};
