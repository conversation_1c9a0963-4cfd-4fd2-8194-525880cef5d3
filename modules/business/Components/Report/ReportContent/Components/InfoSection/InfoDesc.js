import { Ellipsis, Tooltip, Icon, Popover } from 'tntd';
import { isEvalJSON } from '@/utils/isJSON';

export default (props) => {
    const { data } = props || {};
    const { value, label, bizExplain } = data || {};
    const isJsonValue = isEvalJSON(value);
    return (
        <Ellipsis
            className={isJsonValue ? 'is-not-flex' : ''}
            title={isJsonValue ? undefined : value}
            suffix={
                !!isJsonValue && (
                    <Popover
                        placement="left"
                        content={
                            <div style={{ width: 500, maxHeight: 300, overflow: 'auto' }}>
                                <pre className="pre-code-view">{JSON.stringify(eval('(' + value + ')'), null, 2)}</pre>
                            </div>
                        }
                        trigger="hover">
                        <span
                            style={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                flex: 1
                            }}>
                            {value}
                        </span>
                    </Popover>
                )
            }
            prefix={
                <div className="label-title">
                    <Ellipsis
                        suffix={
                            bizExplain && (
                                <Tooltip title={bizExplain}>
                                    <Icon type="question-circle" />
                                </Tooltip>
                            )
                        }
                        title={label}
                    />
                </div>
            }
        />
    );
};
