@import url(../../../variable.less);
.report-section{
    display: flex;
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    border-radius: @border-radius;
    background: @bgColor-light;
    &:not(:last-of-type){
        margin-bottom:16px;
    }
    .first-tag {
        height: 22px;
        margin-left: 6px;
        background: rgba(148,95,185,0.10);
        border: 1px solid rgba(148,95,185,0.4);
        border-radius: 4px;
        color: #945FB9 ;
    }
    .report-section-workflow-chart{
        border-radius: @border-radius;
        width: 100%;
        margin-top: 16px;
        .job-editor.report-job-editor{
            border-radius: @border-radius;
        }
        .job-editor.report-job-editor{
            border: 1px solid @border-color;
            border-radius:@border-radius;
            overflow: hidden;
        }
    }
    .report-policy-name-wrap{
        font-weight: 600;
        .report-policy-name{
            color:#8B919E;
            font-weight: normal;
        }
    }
}

.full-modal {
    .ant-modal-header {
        padding: 12px !important;
    }
    .job-editor.report-job-editor{
        height: calc(100vh - 48px);
    }
    .first-tag{
        height: 22px;
		background: rgba(18, 187, 251, 0.1);
		border: 1px solid #12bbfb;
		border-radius: 4px;
		color: #12bbfb;
    }
}
