import I18N from '~I18N';
import { Table, Tooltip, Tag, Ellipsis, Descriptions } from 'tntd';
import { getFieldByTypeEnum } from '../../../util';
import InfoSection, { InfoTitle } from '../../../Components/InfoSection';
import './index.less';

export default (props) => {
    const { data = {} } = props;

    const columns = [
        {
            title: I18N.rulesettable.index.guiZeMingCheng,
            dataIndex: 'ruleName',
            ellipsis: true,
            width: '50%',
            render: (ruleName, record) => {
                return (
                    <Ellipsis
                        title={ruleName || '- -'}
                        prefix={
                            <>
                                {record.valid === 2 && (
                                    <Tag color="#FFFCF0" className="simulate-tag">
                                        {I18N.rulesettable.index.moNi}
                                    </Tag>
                                )}
                                {record.valid === 1 && (
                                    <Tag color="#E6F4FF" className="formal-tag">
                                        {I18N.rulesettable.index.zhengShi}
                                    </Tag>
                                )}
                                {!!record?.isImmunoRule && <Tag color="green">{I18N.rulesettable.index.mianYi}</Tag>}
                            </>
                        }
                    />
                );
            }
        },
        {
            title: I18N.rulesettable.index.mingZhongDeFenMing,
            dataIndex: 'ruleScoreResult',
            width: '20%',
            ellipsis: true
        },
        {
            title: I18N.rulesettable.index.mingZhongXiangQing,
            dataIndex: 'ruleDetail',
            width: '30%',
            ellipsis: true,
            render: (ruleDetail) => {
                const liDom = ruleDetail?.map((rule, i) => {
                    const typeObj = getFieldByTypeEnum(rule.fieldType);
                    return (
                        <Tooltip key={i} placement="topLeft" title={`${rule.ruleField}:${rule.ruleFieldValue}`}>
                            <li>
                                <span style={{ color: typeObj?.color }}>{typeObj?.typeName}</span>
                                {rule.ruleField}:{rule.ruleFieldValue}
                            </li>
                        </Tooltip>
                    );
                });
                if (liDom) {
                    return <ul className="rule-hit-info">{liDom}</ul>;
                }
                return '- -';
            }
        }
    ];
    return (
        !!data?.length && (
            <InfoSection>
                <Descriptions title={I18N.rulesettable.index.mingZhongXiangQing} />
                <Table
                    shadowed={false}
                    bordered={true}
                    striped={true}
                    className="table-list"
                    columns={columns}
                    dataSource={data || []}
                    pagination={false}
                    rowKey={(e, i) => i}
                />
            </InfoSection>
        )
    );
};
