const fs = require('fs');
const path = require('path');
const _ = require('lodash');

const src = path.resolve(__dirname, './svg');

function capitalizeFirstLetter(str) {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function getSpecifiedFiles(dir) {
    fs.readdirSync(dir).reduce((files, file) => {
        const name = path.join(dir, file);
        const isDirectory = fs.statSync(name).isDirectory();
        const isFile = fs.statSync(name).isFile();

        if (isDirectory) {
            return getSpecifiedFiles(name);
        }

        if (isFile) {
            const svg = readFile(name);
            let fileName = file.split('.')[0];

            fileName = capitalizeFirstLetter(fileName);

            const distPath = path.join(dir, '../lib/' + fileName + '.js');
            writeFile(
                distPath,
                `const ${fileName} = (props) => {
                    return (${svg.replace(/width="32" height="32"/g, 'width="28" height="28" {...props}')});
                };
                export default ${fileName};`
            );
            return isFile;
        }
        return files;
    }, []);
}
function getSpecifiedFiles1(dir) {
    const names = fs.readdirSync(dir).reduce((files, file) => {
        const name = path.join(dir, file);
        const isDirectory = fs.statSync(name).isDirectory();
        const isFile = fs.statSync(name).isFile();

        if (isDirectory) {
            return files.concat(getSpecifiedFiles(name));
        }

        if (isFile) {
            let fileName = file.split('.')[0];

            return files.concat(fileName);
        }
        return files;
    }, []);
    let list = [];
    const componentMap = [];
    for (let index = 0; index < names.length; index++) {
        const element = names[index];
        list.push(`import ${element} from './lib/${element}';`);
        const key = element.toLocaleLowerCase();
        componentMap.push(`${key}:${element}`);
    }
    list = list.join('\r\n');
    const str = `${list}

const componentMap = {
    ${componentMap.join(',\r\n')}
};

const WorkFlowIcon = (props) => {
    const { type } = props;

    const SelectedComponent = componentMap[type];

    const className = 'first-menu-icon first-menu-icon-' + type;

    return SelectedComponent ? <SelectedComponent className={className} {...props} /> : <Start className={'first-menu-icon first-menu-icon-start'} {...props} />;
};

export default WorkFlowIcon;`;

    return str;
}

function getSpecifiedFiles2(dir) {
    const names = fs.readdirSync(dir).reduce((files, file) => {
        const name = path.join(dir, file);
        const isDirectory = fs.statSync(name).isDirectory();
        const isFile = fs.statSync(name).isFile();

        if (isDirectory) {
            return files.concat(getSpecifiedFiles(name));
        }

        if (isFile) {
            let fileName = file.split('.')[0];

            return files.concat(fileName);
        }
        return files;
    }, []);
    let list = [];
    const componentMap = [];
    for (let index = 0; index < names.length; index++) {
        const element = names[index];
        const elementName = capitalizeFirstLetter(element);
        list.push(`import ${elementName} from './svg/${names[index]}.svg';`);
        componentMap.push(`${elementName}:${elementName}`);
    }
    list = list.join('\r\n');
    const str = `${list}

export const workFlowSvgMap = {
    ${componentMap.join(',\r\n')}
};
`;

    return str;
}

function readFile(fileName) {
    if (fs.existsSync(fileName)) {
        return fs.readFileSync(fileName, 'utf-8');
    }
}

function writeFile(filePath, file) {
    const dirPath = path.dirname(filePath);
    if (fs.existsSync(dirPath)) {
        fs.writeFileSync(filePath, file);
    } else {
        fs.mkdirSync(dirPath, { recursive: true });
        fs.writeFileSync(filePath, file);
    }
}

getSpecifiedFiles(src);
const src1 = path.join(src, '../lib');
const ab = getSpecifiedFiles1(src1);

writeFile(path.join(src, '../index.js'), ab);

// 写入Svg
const src2 = path.join(src, '../svg');
const svgInfo = getSpecifiedFiles2(src2);
writeFile(path.join(src, '../svg.js'), svgInfo);
