import common from './common';
import scorecard from './scorecard';
import report from './report';
import exportbtn from './exportbtn';
import reportwrap from './reportwrap';
import reportheader from './reportheader';
import reportcontent from './reportcontent';
import ruleinfo from './ruleinfo';
import rulesettable from './rulesettable';
import indexinfo from './indexinfo';
import childflow from './childflow';
import baseinfo from './baseinfo';
import workflowchart from './workflowchart';
import table from './table';
import popover from './popover';
import mmshapes from './mmshapes';
import mmeditor from './mmeditor';

export default Object.assign({}, {
  common,
  mmeditor,
  mmshapes,
  popover,
  table,
  workflowchart,
  baseinfo,
  childflow,
  indexinfo,
  rulesettable,
  ruleinfo,
  reportcontent,
  reportheader,
  reportwrap,
  exportbtn,
  report,
  scorecard,
});