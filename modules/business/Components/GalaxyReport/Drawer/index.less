.drawer-container {
    padding:24px;
    .drawer-item {
        display:flex;
        flex-direction: column;
        align-items: flex-start;
        gap:var(---LG, 16px);
        border-radius: 8px;
        background-color: #FFF;
        padding:16px;
        .signal-details {
            border-radius: var(---, 8px);
            border: 1px solid var(---Default, #E1E6EE);
            padding: 8px, 16px;
            width:-webkit-fill-available;
        }
        .alarm-valid {
            display:flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }
        .signal-annex{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            .signal-title {
                color:#8B919E;
            }
        }
        
    }
}
.singnal-inner{
    display:flex;
    flex-direction: row;
    align-items: center;
    padding:8px 16px;
    .icon-margin {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        width:40px;
        height: 40px;
    }
    .msg {
        margin-left: 16px;
        .signal-label {
            color:#8B919E;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
        }
        .signal-value {
            color: var(---Text-02, #454F64);
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
        }
    }
}
