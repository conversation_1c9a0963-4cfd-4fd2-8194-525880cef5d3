export default {
  dataconvert: {
    jiXuBuChong: '继续补充',
    ceLuePeiZhiBu: '策略配置不能为空',
    ceLuePeiZhiBu2: '策略配置不合法，原因如下：',
    liuChengMuBan: '流程模板',
    dongZuo: '动作',
    xuYaoPeiZhiZhi: ']需要配置至少2条分支',
    xuYaoPeiZhiTiao: ']需要配置1条冠军分支',
    xuYaoQieZhiNeng: ']需要且只能配置1条默认分支',
    guanJunTiaoZhanZhe: '[冠军挑战者]条件配置未填写完整',
    guanJunTiaoZhanZhe2: '冠军挑战者',
    jueCeGongJuWei: '[决策工具]未设置执行数据',
    jueCeGongJu: '决策工具',
    hanShuWeiSheZhi: '[函数]未设置执行数据',
    hanShu: '函数',
    moXingWeiSheZhi: '[模型]未设置执行数据',
    moXing: '模型',
    sanFangFuWuWei: '[三方服务]未设置执行数据',
    sanFangFuWu: '三方服务',
    guiZeJiWeiShe: '[规则集]未设置执行数据',
    guiZeJi: '规则集',
    xian: '线',
    shuRu: '输入',
    shuChu: '输出',
    zhiShaoPeiZhiTiao: ']至少配置2条',
    kaiShi: '开始',
    jieShu: '结束',
    bingXing: '[并行',
    bingXing2: '并行',
    panDuanKaiShiTiao: '[判断开始]条件配置未填写',
    panDuan: '判断',
    jieShuQueShaoShu: '[结束]缺少输入流',
    kaiShiKaiShiJie: '[开始]开始节点只能有一个',
    kaiShiQueShaoShu: '[开始]缺少输出流',
    queShaoShuChuLiu: ']缺少输出流',
    queShaoShuRuLiu: ']缺少输入流',
    queShaoShuRuShu: ']缺少输入输出流',
    bingXingJieShu: '并行结束',
    bingXingKaiShi: '并行开始',
    panDuanJieShu: '判断结束',
    panDuanKaiShi: '判断开始',
  },
  index: {
    danJiLiuChengMo: '单击流程模板，在页面下方显示轨迹图',
    jieXiShuJuCuo: '解析数据错误,',
  },
};
