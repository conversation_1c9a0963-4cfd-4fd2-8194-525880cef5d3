{"otpDir": "./.octopus", "proType": "react", "srcLang": "zh-CN", "distLangs": ["en-US"], "googleApiKey": "", "baiduApiKey": {"appId": "", "appKey": ""}, "baiduLangMap": {"en-US": "en"}, "translateOptions": {"concurrentLimit": 10, "requestOptions": {}}, "fileSuffix": [".ts", ".js", ".vue", ".jsx", ".tsx"], "defaultTranslateKeyApi": "<PERSON><PERSON><PERSON>", "importI18N": "import I18N from '~I18N';", "include": ["./Components"], "exclude": [], "regPath": "locale", "resourcePath": [], "reservedKey": ["template", "case"]}