import I18N, { getLang } from '~I18N';
import FlowExclusivity from './images/flow-exclusivity.svg';
import FlowParallel from './images/flow-parallel.svg';
import Snap from '../../MMEditor/src/Snap/snap.svg.js';
import { workFlowSvgMap } from '../../../../../../WorkFlowIcon/svg.js';
import { getTextWidth, maxRectWidth } from './utils';

const IconMap = {
    StartFlowNode: workFlowSvgMap['Start'],
    EndFlowNode: workFlowSvgMap['End'],
    ParallelGateway: workFlowSvgMap['Parallel'],
    ExclusiveGateway: workFlowSvgMap['Judgment'],
    SubDecisionFlowNode: workFlowSvgMap['ProcessTemplate'],
    ChildFlowNode: workFlowSvgMap['SubStrategy'],
    RuleSetServiceNode: workFlowSvgMap['Ruleset'],
    ThirdServiceNode: workFlowSvgMap['ThirdPartyService'],
    FeatureServiceNode: workFlowSvgMap['ThirdPartyService'],
    DecisionToolServiceNode: workFlowSvgMap['DecisionTool'],
    ModelServiceNode: workFlowSvgMap['Model'],
    FunctionServiceNode: workFlowSvgMap['Function'],
    ChampionServiceNode: workFlowSvgMap['ChampionChallenger'],
    ActionServiceNode: workFlowSvgMap['Action'],
    ScoreCardServiceNode: workFlowSvgMap['ScoreCard'],
    SuspendFlowNode: workFlowSvgMap['ContinueSupplement'],
    RouteServiceNode: workFlowSvgMap['ChampionChallenger'],
    DataTableServiceNode: workFlowSvgMap['ScoreCard']
};

export default function initShapes(editor) {
    // 渲染策略类节点
    const renderNode = (data, snapPaper, opt) => {
        const { name: namePre } = data;
        let name = namePre;

        //是否需要多行展示
        let textW = getTextWidth(name, '12px');
        let isWrap;
        if (textW >= maxRectWidth - 40) {
            isWrap = true;
        } else {
            //最小宽度为100
            textW = Math.max(textW, 60);
        }

        // let text1;
        // if (getLang() === 'cn') {
        //     text1 = snapPaper.text(15, isWrap ? 21 : 15, opt.iconText);
        // } else {
        //     text1 = snapPaper.text(15, isWrap ? 21 : 15, opt.iconText.substring(0, 1));
        // }
        // text1.attr({
        //     fill: '#fff'
        // });
        // text1?.node?.setAttribute('font-size', '12px');

        const image = snapPaper.image(IconMap[opt.type], 0, 0, 16, 16);
        image.node.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');
        image.attr({
            x: 7,
            y: isWrap ? 12 : 6
        });

        const circle = snapPaper.circle(15, isWrap ? 20 : 14, 11);
        circle.attr({
            fill: opt.color
        });
        const circleGroup = snapPaper.group(circle, image);
        // const text = snapPaper.text(30, 15, name);
        //文本宽度 >=节点宽度100 - icon宽度，则需要换行,最多支持两行显示

        // const node = snapPaper.rect(0, 0, isWrap ? maxRectWidth : textW + 40, isWrap ? 40 : 28, 15, 15);
        const node = snapPaper.rect(0, 0, maxRectWidth, isWrap ? 40 : 28, 15, 15);
        node.attr({
            fill: '#ECF0FA',
            stroke: '#fff',
            class: 'flow-icon-node',
            strokeWidth: 2,
            filter: 'url(#mm-editor-node-shadow)'
        });

        //右上角图标
        const obj = document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject');
        obj.innerHTML = '<span xmlns="http://www.w3.org/1999/xhtml" class="mm-highlight"></span>';
        const statusIcon = window.Snap(obj);
        statusIcon.attr({
            width: 18,
            height: 18,
            x: node.getBBox().width - 14,
            y: -4
        });
        //文字
        const obj1 = document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject');
        obj1.innerHTML = `<div class="wrap-txt-node">${namePre}</div>`;
        const statusIcon1 = window.Snap(obj1);

        statusIcon1.attr({
            width: maxRectWidth - 40, //isWrap ? maxRectWidth - 40 : textW + 5,
            height: isWrap ? 30 : 15,
            x: 30,
            y: 6
        });

        obj1.firstChild.setAttribute('width', isWrap ? maxRectWidth - 40 : textW);

        return snapPaper.group(node, circleGroup, statusIcon, statusIcon1);
    };

    // 初始化组件
    const initEditorShape = () => {
        // 开始
        editor.graph.node.registeNode(
            'StartFlowNode',
            {
                render: (data, snapPaper) => {
                    const node = snapPaper.circle(24, 24, 24);
                    const text = snapPaper.text(24, 24, data.name);
                    node.attr({
                        fill: '#20BD9F',
                        class: 'flow-icon-node',
                        filter: 'url(#mm-editor-node-shadow)',
                        stroke: '#fff',
                        strokeWidth: 2
                    });
                    text.attr({
                        fill: '#fff',
                        class: 'flow-txt-node'
                    });
                    text?.node?.setAttribute('font-size', '12px');
                    return snapPaper.group(node, text);
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'default'
        );
        // 继续补充
        editor.graph.node.registeNode(
            'SuspendFlowNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.ji,
                        type: 'SuspendFlowNode',
                        color: '#E6B55E'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        //  结束
        editor.graph.node.registeNode(
            'EndFlowNode',
            {
                render: (data, snapPaper) => {
                    const node = snapPaper.circle(25, 25, 25);
                    const text = snapPaper.text(25, 25, data.name);
                    node.attr({
                        fill: '#8B919E',
                        class: 'flow-icon-node',
                        filter: 'url(#mm-editor-node-shadow)',
                        stroke: '#fff',
                        strokeWidth: 2
                    });
                    text.attr({
                        fill: '#fff',
                        class: 'flow-txt-node'
                    });
                    text?.node?.setAttribute('font-size', '12px');
                    return snapPaper.group(node, text);
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'default'
        );

        // 判断
        editor.graph.node.registeNode(
            'ExclusiveGateway',
            {
                render: (data, snapPaper) => {
                    const image = snapPaper.image(FlowExclusivity, 0, 0, 120, 52);
                    image.node.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');
                    image.attr({
                        filter: 'url(#mm-editor-node-shadow)'
                    });
                    let dName = data.name;
                    if (dName.length > 5) {
                        dName = dName.slice(0, 5) + '...';
                    }

                    const text = snapPaper.text(62, 28, dName);
                    text.attr({
                        fill: '#F47345',
                        class: 'flow-txt-node'
                    });
                    text?.node?.setAttribute('font-size', '12px');

                    const iconImg = Snap(18, 18);
                    Snap.load(IconMap['ExclusiveGateway'], (f) => {
                        // 将加载的图片添加到 Snap.svg 的实例中
                        iconImg.append(f);
                    });
                    iconImg.attr({
                        x: 20,
                        y: 18,
                        class: 'exclusive-node-icon'
                    });
                    return snapPaper.group(image, iconImg, text);
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'default'
        );
        // 并行
        editor.graph.node.registeNode(
            'ParallelGateway',
            {
                render: (data, snapPaper) => {
                    const image = snapPaper.image(FlowParallel, 0, 0, 120, 52);
                    image.node.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');
                    image.attr({
                        filter: 'url(#mm-editor-node-shadow)'
                    });
                    let dName = data.name;
                    if (dName.length > 5) {
                        dName = dName.slice(0, 5) + '...';
                    }
                    const text = snapPaper.text(62, 28, dName);
                    text.attr({
                        fill: '#7A5AF8',
                        class: 'flow-txt-node'
                    });
                    text?.node?.setAttribute('font-size', '12px');

                    const iconImg = Snap(18, 18);
                    Snap.load(IconMap['ParallelGateway'], (f) => {
                        // 将加载的图片添加到 Snap.svg 的实例中
                        iconImg.append(f);
                    });
                    iconImg.attr({
                        x: 20,
                        y: 18,
                        class: 'parallel-node-icon'
                    });
                    return snapPaper.group(image, iconImg, text);
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'default'
        );
        // 流程模板
        editor.graph.node.registeNode(
            'SubDecisionFlowNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.liu,
                        color: '#628fe4',
                        type: 'SubDecisionFlowNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 子策略
        editor.graph.node.registeNode(
            'ChildFlowNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.liu,
                        color: '#36BFFA',
                        type: 'SubDecisionFlowNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 规则
        editor.graph.node.registeNode(
            'RuleSetServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.gui,
                        color: '#628FE4',
                        type: 'RuleSetServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 三方服务
        editor.graph.node.registeNode(
            'ThirdServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.san,
                        color: '#CEAB67',
                        type: 'ThirdServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );
        // 三方服务
        editor.graph.node.registeNode(
            'FeatureServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.san,
                        color: '#CFAB67',
                        type: 'FeatureServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 决策工具
        editor.graph.node.registeNode(
            'DecisionToolServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.gong,
                        color: '#CF6767',
                        type: 'DecisionToolServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 准入模型
        editor.graph.node.registeNode(
            'ModelServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.mo,
                        color: '#4DA9C9',
                        type: 'ModelServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 函数
        editor.graph.node.registeNode(
            'FunctionServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.han,
                        color: '#D97B4E',
                        type: 'FunctionServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 冠军挑战者
        editor.graph.node.registeNode(
            'ChampionServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.guan,
                        color: '#6E54D4',
                        type: 'ChampionServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 智能路由
        editor.graph.node.registeNode(
            'RouteServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: '智',
                        color: '#6E54D4',
                        type: 'RouteServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        // 动作
        editor.graph.node.registeNode(
            'ActionServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.dong,
                        color: '#6172F3',
                        type: 'ActionServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );

        editor.graph.node.registeNode(
            'ChildFlowNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.zi,
                        color: '#36BFFA',
                        type: 'ChildFlowNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );
        // 评分卡
        editor.graph.node.registeNode(
            'ScoreCardServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: I18N.mmshapes.initshapes.ping,
                        color: '#E844B7',
                        type: 'ScoreCardServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );
        editor.graph.node.registeNode(
            'DataTableServiceNode',
            {
                render: (data, snapPaper) => {
                    return renderNode(data, snapPaper, {
                        iconText: '表',
                        color: '#E844B7',
                        type: 'DataTableServiceNode'
                    });
                },
                linkPoints: [
                    { x: 0.5, y: 0 },
                    { x: 1, y: 0.5 },
                    { x: 0.5, y: 1 },
                    { x: 0, y: 0.5 }
                ]
            },
            'iconNode'
        );
    };
    initEditorShape();
}
