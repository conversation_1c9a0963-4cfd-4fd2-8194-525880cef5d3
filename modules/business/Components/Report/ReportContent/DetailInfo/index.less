@import url(../../variable.less);
@import url(./description.less);
.report-detail-wrap{
    background: #FFF;
    box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.09);
    border-radius: calc(@border-radius-base * 2);
    margin-top:12px;
    padding: 8px 16px;
    .report-tabs{
        .ant-tabs-bar{
            margin-bottom: 0;;
        }
        .ant-tabs-nav-container-scrolling{
            .ant-tabs-tab-prev,.ant-tabs-tab-next{
                background-color: #fff;
            }
            // .ant-tabs-nav-scroll{

            //     .ant-tabs-nav {
            //         .ant-tabs-tab{
            //             padding: 1px 12px 0px 12px;
            //             position: relative;
            //             height: 40px;
            //             line-height: 40px;
            //         }
            //     }
            // }
        }
    }
    .report-detail-tab-content{
        padding:16px 0;
    }
    .pad-16{
        padding: 16px;
    }
}
